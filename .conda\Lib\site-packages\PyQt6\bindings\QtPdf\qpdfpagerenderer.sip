// qpdfpagerenderer.sip generated by MetaSIP
//
// This file is part of the QtPdf Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPdfPageRenderer : public QObject
{
%TypeHeaderCode
#include <qpdfpagerenderer.h>
%End

public:
    enum class RenderMode
    {
        MultiThreaded,
        SingleThreaded,
    };

    explicit QPdfPageRenderer(QObject *parent /TransferThis/);
    virtual ~QPdfPageRenderer();
    QPdfPageRenderer::RenderMode renderMode() const;
    void setRenderMode(QPdfPageRenderer::RenderMode mode);
    QPdfDocument *document() const;
    void setDocument(QPdfDocument *document);
    quint64 requestPage(int pageNumber, QSize imageSize, QPdfDocumentRenderOptions options = QPdfDocumentRenderOptions());

signals:
    void documentChanged(QPdfDocument *document);
    void renderModeChanged(QPdfPageRenderer::RenderMode renderMode);
};
