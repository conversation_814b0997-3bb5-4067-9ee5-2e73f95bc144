// qmediarecorder.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QMediaRecorder : public QObject
{
%TypeHeaderCode
#include <qmediarecorder.h>
%End

public:
    enum Quality
    {
        VeryLowQuality,
        LowQuality,
        NormalQuality,
        HighQuality,
        VeryHighQuality,
    };

    enum EncodingMode
    {
        ConstantQualityEncoding,
        ConstantBitRateEncoding,
        AverageBitRateEncoding,
        TwoPassEncoding,
    };

    enum RecorderState
    {
        StoppedState,
        RecordingState,
        PausedState,
    };

    enum Error
    {
        NoError,
        ResourceError,
        FormatError,
        OutOfSpaceError,
        LocationNotWritable,
    };

    QMediaRecorder(QObject *parent /TransferThis/ = 0);
    virtual ~QMediaRecorder();
    bool isAvailable() const;
    QUrl outputLocation() const;
    void setOutputLocation(const QUrl &location);
    QUrl actualLocation() const;
    QMediaRecorder::RecorderState recorderState() const;
    QMediaRecorder::Error error() const;
    QString errorString() const;
    qint64 duration() const;
    QMediaFormat mediaFormat() const;
    void setMediaFormat(const QMediaFormat &format);
    QMediaRecorder::EncodingMode encodingMode() const;
    void setEncodingMode(QMediaRecorder::EncodingMode);
    QMediaRecorder::Quality quality() const;
    void setQuality(QMediaRecorder::Quality quality);
    QSize videoResolution() const;
    void setVideoResolution(const QSize &);
    void setVideoResolution(int width, int height);
    qreal videoFrameRate() const;
    void setVideoFrameRate(qreal frameRate);
    int videoBitRate() const;
    void setVideoBitRate(int bitRate);
    int audioBitRate() const;
    void setAudioBitRate(int bitRate);
    int audioChannelCount() const;
    void setAudioChannelCount(int channels);
    int audioSampleRate() const;
    void setAudioSampleRate(int sampleRate);
    QMediaMetaData metaData() const;
    void setMetaData(const QMediaMetaData &metaData);
    QMediaCaptureSession *captureSession() const;

public slots:
    void record();
    void pause();
    void stop();

signals:
    void recorderStateChanged(QMediaRecorder::RecorderState state /ScopesStripped=1/);
    void durationChanged(qint64 duration);
    void actualLocationChanged(const QUrl &location);
    void errorOccurred(QMediaRecorder::Error error /ScopesStripped=1/, const QString &errorString);
    void errorChanged();
    void metaDataChanged();
    void mediaFormatChanged();
    void encodingModeChanged();
    void qualityChanged();
    void videoResolutionChanged();
    void videoFrameRateChanged();
    void videoBitRateChanged();
    void audioBitRateChanged();
    void audioChannelCountChanged();
    void audioSampleRateChanged();

public:
%If (Qt_6_8_0 -)
    void setOutputDevice(QIODevice *device);
%End
%If (Qt_6_8_0 -)
    QIODevice *outputDevice() const;
%End
%If (Qt_6_8_0 -)
    bool autoStop() const;
%End
%If (Qt_6_8_0 -)
    void setAutoStop(bool autoStop);
%End

signals:
%If (Qt_6_8_0 -)
    void autoStopChanged();
%End
};

%End
