// qiconengine.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QIconEngine /Supertype=PyQt6.sip.wrapper/
{
%TypeHeaderCode
#include <qiconengine.h>
%End

public:
    QIconEngine();

protected:
    QIconEngine(const QIconEngine &other);

public:
    virtual ~QIconEngine();
    virtual void paint(QPainter *painter, const QRect &rect, QIcon::Mode mode, QIcon::State state) = 0;
    virtual QSize actualSize(const QSize &size, QIcon::Mode mode, QIcon::State state);
    virtual QPixmap pixmap(const QSize &size, QIcon::Mode mode, QIcon::State state);
    virtual void addPixmap(const QPixmap &pixmap, QIcon::Mode mode, QIcon::State state);
    virtual void addFile(const QString &fileName, const QSize &size, QIcon::Mode mode, QIcon::State state);
    virtual QString key() const;
    virtual QIconEngine *clone() const = 0 /Factory/;
    virtual bool read(QDataStream &in);
    virtual bool write(QDataStream &out) const;

    enum IconEngineHook
    {
        IsNullHook,
        ScaledPixmapHook,
    };

    virtual QList<QSize> availableSizes(QIcon::Mode mode = QIcon::Normal, QIcon::State state = QIcon::Off);
    virtual QString iconName();
    virtual bool isNull();
    virtual QPixmap scaledPixmap(const QSize &size, QIcon::Mode mode, QIcon::State state, qreal scale);

    struct ScaledPixmapArgument
    {
%TypeHeaderCode
#include <qiconengine.h>
%End

        QSize size;
        QIcon::Mode mode;
        QIcon::State state;
        qreal scale;
        QPixmap pixmap;
    };
};
