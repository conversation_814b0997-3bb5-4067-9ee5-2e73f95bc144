// qhttpmultipart.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QHttpPart
{
%TypeHeaderCode
#include <qhttpmultipart.h>
%End

public:
    QHttpPart();
    QHttpPart(const QHttpPart &other);
    ~QHttpPart();
    bool operator==(const QHttpPart &other) const;
    bool operator!=(const QHttpPart &other) const;
    void setHeader(QNetworkRequest::KnownHeaders header, const QVariant &value);
    void setRawHeader(const QByteArray &headerName, const QByteArray &headerValue);
    void setBody(const QByteArray &body);
    void setBodyDevice(QIODevice *device);
    void swap(QHttpPart &other /Constrained/);
};

class QHttpMultiPart : public QObject
{
%TypeHeaderCode
#include <qhttpmultipart.h>
%End

public:
    enum ContentType
    {
        MixedType,
        RelatedType,
        FormDataType,
        AlternativeType,
    };

    explicit QHttpMultiPart(QObject *parent /TransferThis/ = 0);
    QHttpMultiPart(QHttpMultiPart::ContentType contentType, QObject *parent /TransferThis/ = 0);
    virtual ~QHttpMultiPart();
    void append(const QHttpPart &httpPart);
    void setContentType(QHttpMultiPart::ContentType contentType);
    QByteArray boundary() const;
    void setBoundary(const QByteArray &boundary);
};
