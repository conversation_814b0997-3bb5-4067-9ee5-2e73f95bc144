// qtimeline.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QTimeLine : public QObject
{
%TypeHeaderCode
#include <qtimeline.h>
%End

public:
    enum Direction
    {
        Forward,
        Backward,
    };

    enum State
    {
        NotRunning,
        Paused,
        Running,
    };

    QTimeLine(int duration = 1000, QObject *parent /TransferThis/ = 0);
    virtual ~QTimeLine();
    QTimeLine::State state() const;
    int loopCount() const;
    void setLoopCount(int count);
    QTimeLine::Direction direction() const;
    void setDirection(QTimeLine::Direction direction);
    int duration() const;
    void setDuration(int duration);
    int startFrame() const;
    void setStartFrame(int frame);
    int endFrame() const;
    void setEndFrame(int frame);
    void setFrameRange(int startFrame, int endFrame);
    int updateInterval() const;
    void setUpdateInterval(int interval);
    int currentTime() const;
    int currentFrame() const;
    qreal currentValue() const;
    int frameForTime(int msec) const;
    virtual qreal valueForTime(int msec) const;

public slots:
    void resume();
    void setCurrentTime(int msec);
    void setPaused(bool paused);
    void start();
    void stop();
    void toggleDirection();

signals:
    void finished();
    void frameChanged(int);
    void stateChanged(QTimeLine::State newState);
    void valueChanged(qreal x);

protected:
    virtual void timerEvent(QTimerEvent *event);

public:
    QEasingCurve easingCurve() const;
    void setEasingCurve(const QEasingCurve &curve);
};
