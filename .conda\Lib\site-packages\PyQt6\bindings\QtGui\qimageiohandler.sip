// qimageiohandler.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QImageIOHandler
{
%TypeHeaderCode
#include <qimageiohandler.h>
%End

public:
    enum ImageOption
    {
        Size,
        ClipRect,
        Description,
        ScaledClipRect,
        ScaledSize,
        CompressionRatio,
        Gamma,
        Quality,
        Name,
        SubType,
        IncrementalReading,
        Endianness,
        Animation,
        BackgroundColor,
        SupportedSubTypes,
        OptimizedWrite,
        ProgressiveScanWrite,
        ImageTransformation,
    };

    QImageIOHandler();
    virtual ~QImageIOHandler();
    void setDevice(QIODevice *device);
    QIODevice *device() const;
    void setFormat(const QByteArray &format);
    QByteArray format() const;
    virtual bool canRead() const = 0;
    virtual bool read(QImage *image) = 0;
    virtual bool write(const QImage &image);
    virtual QVariant option(QImageIOHandler::ImageOption option) const;
    virtual void setOption(QImageIOHandler::ImageOption option, const QVariant &value);
    virtual bool supportsOption(QImageIOHandler::ImageOption option) const;
    virtual bool jumpToNextImage();
    virtual bool jumpToImage(int imageNumber);
    virtual int loopCount() const;
    virtual int imageCount() const;
    virtual int nextImageDelay() const;
    virtual int currentImageNumber() const;
    virtual QRect currentImageRect() const;

    enum Transformation /BaseType=Flag/
    {
        TransformationNone,
        TransformationMirror,
        TransformationFlip,
        TransformationRotate180,
        TransformationRotate90,
        TransformationMirrorAndRotate90,
        TransformationFlipAndRotate90,
        TransformationRotate270,
    };

    typedef QFlags<QImageIOHandler::Transformation> Transformations;

private:
    QImageIOHandler(const QImageIOHandler &);
};
