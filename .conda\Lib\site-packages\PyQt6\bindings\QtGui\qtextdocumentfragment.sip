// qtextdocumentfragment.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QTextDocumentFragment
{
%TypeHeaderCode
#include <qtextdocumentfragment.h>
%End

public:
    QTextDocumentFragment();
    explicit QTextDocumentFragment(const QTextDocument *document);
    explicit QTextDocumentFragment(const QTextCursor &range);
    QTextDocumentFragment(const QTextDocumentFragment &rhs);
    ~QTextDocumentFragment();
    bool isEmpty() const;
    QString toPlainText() const;
    QString toHtml() const;
    static QTextDocumentFragment fromPlainText(const QString &plainText);
    static QTextDocumentFragment fromHtml(const QString &html, const QTextDocument *resourceProvider = 0);
%If (Qt_6_4_0 -)
    QString toRawText() const;
%End
%If (Qt_6_4_0 -)
    QString toMarkdown(QTextDocument::MarkdownFeatures features = QTextDocument::MarkdownDialectGitHub) const;
%End
%If (Qt_6_4_0 -)
    static QTextDocumentFragment fromMarkdown(const QString &markdown, QTextDocument::MarkdownFeatures features = QTextDocument::MarkdownDialectGitHub);
%End
};
