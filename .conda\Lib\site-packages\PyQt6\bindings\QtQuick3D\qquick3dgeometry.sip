// qquick3dgeometry.sip generated by MetaSIP
//
// This file is part of the QtQuick3D Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QQuick3DGeometry : public QQuick3DObject
{
%TypeHeaderCode
#include <qquick3dgeometry.h>
%End

public:
    explicit QQuick3DGeometry(QQuick3DObject *parent /TransferThis/ = 0);
    virtual ~QQuick3DGeometry();

    enum class PrimitiveType
    {
        Points,
        LineStrip,
        Lines,
        TriangleStrip,
        TriangleFan,
        Triangles,
    };

    struct Attribute
    {
%TypeHeaderCode
#include <qquick3dgeometry.h>
%End

        enum Semantic
        {
            IndexSemantic,
            PositionSemantic,
            NormalSemantic,
            TexCoordSemantic,
            TangentSemantic,
            BinormalSemantic,
            JointSemantic,
            WeightSemantic,
            ColorSemantic,
%If (Qt_6_1_0 -)
            TargetPositionSemantic,
%End
%If (Qt_6_1_0 -)
            TargetNormalSemantic,
%End
%If (Qt_6_1_0 -)
            TargetTangentSemantic,
%End
%If (Qt_6_1_0 -)
            TargetBinormalSemantic,
%End
%If (Qt_6_1_0 -)
            TexCoord1Semantic,
%End
%If (Qt_6_1_0 -)
            TexCoord0Semantic,
%End
        };

        enum ComponentType
        {
            U16Type,
            U32Type,
            F32Type,
            I32Type,
        };

        QQuick3DGeometry::Attribute::Semantic semantic;
        int offset;
        QQuick3DGeometry::Attribute::ComponentType componentType;
    };

    int attributeCount() const;
    QQuick3DGeometry::Attribute attribute(int index) const;
    QQuick3DGeometry::PrimitiveType primitiveType() const;
    QVector3D boundsMin() const;
    QVector3D boundsMax() const;
    int stride() const;
    void setVertexData(const QByteArray &data);
    void setVertexData(int offset, const QByteArray &data);
    void setIndexData(const QByteArray &data);
    void setIndexData(int offset, const QByteArray &data);
    void setStride(int stride);
    void setBounds(const QVector3D &min, const QVector3D &max);
    void setPrimitiveType(QQuick3DGeometry::PrimitiveType type);
    void addAttribute(QQuick3DGeometry::Attribute::Semantic semantic, int offset, QQuick3DGeometry::Attribute::ComponentType componentType);
    void addAttribute(const QQuick3DGeometry::Attribute &att);
    void clear();
    QByteArray vertexData() const;
    QByteArray indexData() const;
%If (Qt_6_3_0 -)
    int subsetCount(int subset) const;
%End
%If (Qt_6_3_0 -)
    int subsetCount() const;
%End
%If (Qt_6_3_0 -)
    QVector3D subsetBoundsMin(int subset) const;
%End
%If (Qt_6_3_0 -)
    QVector3D subsetBoundsMax(int subset) const;
%End
%If (Qt_6_3_0 -)
    int subsetOffset(int subset) const;
%End
%If (Qt_6_3_0 -)
    QString subsetName(int subset) const;
%End
%If (Qt_6_3_0 -)
    void addSubset(int offset, int count, const QVector3D &boundsMin, const QVector3D &boundsMax, const QString &name = {});
%End
%If (Qt_6_6_0 -)

    struct TargetAttribute
    {
%TypeHeaderCode
#include <qquick3dgeometry.h>
%End

        quint32 targetId;
        QQuick3DGeometry::Attribute attr;
        int stride;
    };

%End
%If (Qt_6_6_0 -)
    QByteArray targetData() const;
%End
%If (Qt_6_6_0 -)
    void setTargetData(int offset, const QByteArray &data);
%End
%If (Qt_6_6_0 -)
    void setTargetData(const QByteArray &data);
%End
%If (Qt_6_6_0 -)
    QQuick3DGeometry::TargetAttribute targetAttribute(int index) const;
%End
%If (Qt_6_6_0 -)
    int targetAttributeCount() const;
%End
%If (Qt_6_6_0 -)
    void addTargetAttribute(const QQuick3DGeometry::TargetAttribute &att);
%End
%If (Qt_6_6_0 -)
    void addTargetAttribute(quint32 targetId, QQuick3DGeometry::Attribute::Semantic semantic, int offset, int stride = 0);
%End
};
