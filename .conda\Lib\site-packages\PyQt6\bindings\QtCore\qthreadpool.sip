// qthreadpool.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QThreadPool : public QObject
{
%TypeHeaderCode
#include <qthreadpool.h>
%End

public:
    QThreadPool(QObject *parent /TransferThis/ = 0);
    virtual ~QThreadPool() /ReleaseGIL/;
    static QThreadPool *globalInstance() /KeepReference/;
    void start(QRunnable *runnable /GetWrapper/, int priority = 0) /ReleaseGIL/;
%MethodCode
        // We have to handle the object ownership manually.
        if (a0->autoDelete())
            sipTransferTo(a0Wrapper, sipSelf);
        
        Py_BEGIN_ALLOW_THREADS
        sipCpp->start(a0, a1);
        Py_END_ALLOW_THREADS
%End

    void start(SIP_PYCALLABLE functionToRun /TypeHint="Callable[[], None]"/, int priority = 0) /ReleaseGIL/;
%MethodCode
        Py_INCREF(a0);
        
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->start([a0]() {
            SIP_BLOCK_THREADS
        
            PyObject *res;
        
            res = PyObject_CallObject(a0, NULL);
        
            Py_DECREF(a0);
        
            if (res)
                Py_DECREF(res);
            else
                pyqt6_err_print();
        
            SIP_UNBLOCK_THREADS
        }, a1);
        
        Py_END_ALLOW_THREADS
%End

    bool tryStart(QRunnable *runnable /GetWrapper/) /ReleaseGIL/;
%MethodCode
        // We have to handle the object ownership manually.
        if (a0->autoDelete())
            sipTransferTo(a0Wrapper, sipSelf);
        
        Py_BEGIN_ALLOW_THREADS
        sipRes = sipCpp->tryStart(a0);
        Py_END_ALLOW_THREADS
%End

    bool tryStart(SIP_PYCALLABLE functionToRun /TypeHint="Callable[[], None]"/) /ReleaseGIL/;
%MethodCode
        Py_INCREF(a0);
        
        Py_BEGIN_ALLOW_THREADS
        
        sipRes = sipCpp->tryStart([a0]() {
            SIP_BLOCK_THREADS
        
            PyObject *res;
        
            res = PyObject_CallObject(a0, NULL);
        
            Py_DECREF(a0);
        
            if (res)
                Py_DECREF(res);
            else
                pyqt6_err_print();
        
            SIP_UNBLOCK_THREADS
        });
        
        Py_END_ALLOW_THREADS
%End

    bool tryTake(QRunnable *runnable /GetWrapper/) /ReleaseGIL/;
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        sipRes = sipCpp->tryTake(a0);
        Py_END_ALLOW_THREADS
        
        // We have to handle the object ownership manually.
        if (sipRes)
            sipTransferBack(a0Wrapper);
%End

    int expiryTimeout() const;
    void setExpiryTimeout(int expiryTimeout);
    int maxThreadCount() const;
    void setMaxThreadCount(int maxThreadCount) /ReleaseGIL/;
    int activeThreadCount() const /ReleaseGIL/;
    void reserveThread() /ReleaseGIL/;
    void releaseThread() /ReleaseGIL/;
%If (Qt_6_8_0 -)
    bool waitForDone(QDeadlineTimer deadline) /ReleaseGIL/ [bool (QDeadlineTimer deadline = QDeadlineTimer::Forever)];
%End
    bool waitForDone(int msecs = -1) /ReleaseGIL/;
    void clear() /ReleaseGIL/;
    void setStackSize(uint stackSize);
    uint stackSize() const;
    bool contains(const QThread *thread) const;
%If (Qt_6_2_0 -)
    void setThreadPriority(QThread::Priority priority);
%End
%If (Qt_6_2_0 -)
    QThread::Priority threadPriority() const;
%End
%If (Qt_6_3_0 -)
    void startOnReservedThread(QRunnable *runnable /GetWrapper/) /ReleaseGIL/;
%MethodCode
        // We have to handle the object ownership manually.
        if (a0->autoDelete())
            sipTransferTo(a0Wrapper, sipSelf);
        
        Py_BEGIN_ALLOW_THREADS
        sipCpp->startOnReservedThread(a0);
        Py_END_ALLOW_THREADS
%End

%End
%If (Qt_6_3_0 -)
    void startOnReservedThread(SIP_PYCALLABLE functionToRun /TypeHint="Callable[[], None]"/) /ReleaseGIL/;
%MethodCode
        Py_INCREF(a0);
        
        Py_BEGIN_ALLOW_THREADS
        
        sipCpp->startOnReservedThread([a0]() {
            SIP_BLOCK_THREADS
        
            PyObject *res;
        
            res = PyObject_CallObject(a0, NULL);
        
            Py_DECREF(a0);
        
            if (res)
                Py_DECREF(res);
            else
                pyqt6_err_print();
        
            SIP_UNBLOCK_THREADS
        });
        
        Py_END_ALLOW_THREADS
%End

%End
%If (Qt_6_9_0 -)
    void setServiceLevel(QThread::QualityOfService serviceLevel);
%End
%If (Qt_6_9_0 -)
    QThread::QualityOfService serviceLevel() const;
%End
};
