// qlowenergycharacteristicdata.sip generated by MetaSIP
//
// This file is part of the QtBluetooth Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QLowEnergyCharacteristicData
{
%TypeHeaderCode
#include <qlowenergycharacteristicdata.h>
%End

public:
    QLowEnergyCharacteristicData();
    QLowEnergyCharacteristicData(const QLowEnergyCharacteristicData &other);
    ~QLowEnergyCharacteristicData();
    QBluetoothUuid uuid() const;
    void setUuid(const QBluetoothUuid &uuid);
    QByteArray value() const;
    void setValue(const QByteArray &value);
    QLowEnergyCharacteristic::PropertyTypes properties() const;
    void setProperties(QLowEnergyCharacteristic::PropertyTypes properties);
    QList<QLowEnergyDescriptorData> descriptors() const;
    void setDescriptors(const QList<QLowEnergyDescriptorData> &descriptors);
    void addDescriptor(const QLowEnergyDescriptorData &descriptor);
    void setReadConstraints(QBluetooth::AttAccessConstraints constraints);
    QBluetooth::AttAccessConstraints readConstraints() const;
    void setWriteConstraints(QBluetooth::AttAccessConstraints constraints);
    QBluetooth::AttAccessConstraints writeConstraints() const;
    void setValueLength(int minimum, int maximum);
    int minimumValueLength() const;
    int maximumValueLength() const;
    bool isValid() const;
    void swap(QLowEnergyCharacteristicData &other);
};

%End
%If (Qt_6_2_0 -)
bool operator==(const QLowEnergyCharacteristicData &cd1, const QLowEnergyCharacteristicData &cd2);
%End
%If (Qt_6_2_0 -)
bool operator!=(const QLowEnergyCharacteristicData &cd1, const QLowEnergyCharacteristicData &cd2);
%End
