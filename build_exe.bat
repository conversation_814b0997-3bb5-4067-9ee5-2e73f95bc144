@echo off
echo ========================================
echo 文件自动分类工具 2.0 - 打包脚本
echo ========================================
echo.

echo [1/5] 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: 未找到Python环境
    pause
    exit /b 1
)

echo.
echo [2/5] 安装依赖包...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo 错误: 依赖包安装失败
    pause
    exit /b 1
)

echo.
echo [3/5] 清理旧的构建文件...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"

echo.
echo [4/5] 开始打包程序...
pyinstaller build_gui.spec
if %errorlevel% neq 0 (
    echo 错误: 打包失败
    pause
    exit /b 1
)

echo.
echo [5/5] 复制配置文件到输出目录...
if exist "categories_config.json" copy "categories_config.json" "dist\"
if exist "gui_config.json" copy "gui_config.json" "dist\"

echo.
echo ========================================
echo 打包完成！
echo 可执行文件位置: dist\文件自动分类工具2.0.exe
echo ========================================
echo.

echo 是否立即运行程序进行测试？(Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    echo 启动程序...
    start "" "dist\文件自动分类工具2.0.exe"
)

pause
