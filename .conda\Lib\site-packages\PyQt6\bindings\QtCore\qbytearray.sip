// qbytearray.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%ModuleCode
#include <qbytearray.h>
%End

class QByteArray /TypeHintIn="Union[QByteArray, bytes, bytearray, memoryview]"/
{
%TypeHeaderCode
#include <qbytearray.h>
%End

%TypeCode
// This is needed by __hash__().
#include <qhash.h>


// Convenience function for converting a QByteArray to a Python str object.
static PyObject *QByteArrayToPyStr(QByteArray *ba)
{
    char *data = ba->data();

    if (data)
        // QByteArrays may have embedded '\0's so set the size explicitly.    
        return PyBytes_FromStringAndSize(data, ba->size());

    return PyBytes_FromString("");
}
%End

%ConvertToTypeCode
// Accept anything that implements the buffer protocol (including QByteArray
// itself).

if (sipIsErr == NULL)
    return sipGetBufferInfo(sipPy, NULL);


// If it is already a QByteArray then use it rather than make an unnecessary
// copy of the data.
if (sipCanConvertToType(sipPy, sipType_QByteArray, SIP_NO_CONVERTORS))
{
    *sipCppPtr = reinterpret_cast<QByteArray *>(
            sipConvertToType(sipPy, sipType_QByteArray, sipTransferObj,
            SIP_NO_CONVERTORS, 0, sipIsErr));

    return 0;
}

// Create a QByteArray from the data.
sipBufferInfoDef buffer_info;

if (sipGetBufferInfo(sipPy, &buffer_info) > 0)
{
    // Check that the buffer is compatible with one defined by
    // PyBuffer_FillInfo() as used by QByteArray and the standard Python byte
    // objects.
    if (buffer_info.bi_format == NULL || buffer_info.bi_format[0] == 'B')
    {
        *sipCppPtr = new QByteArray(
                reinterpret_cast<const char *>(buffer_info.bi_buf),
                (qsizetype)buffer_info.bi_len);

        sipReleaseBufferInfo(&buffer_info);

        // We don't support transfer of ownership.
        return SIP_TEMPORARY;
    }

    PyErr_Format(PyExc_TypeError, "unsupported buffer format '%s'",
            buffer_info.bi_format);

    sipReleaseBufferInfo(&buffer_info);
}

*sipIsErr = 1;

return 0;
%End

%BIGetBufferCode
    // We may be building against a debug Python build.
    
    #if defined(Py_LIMITED_API)
        Q_UNUSED(sipSelf);
    
        sipBuffer->bd_buffer = sipCpp->data();
        sipBuffer->bd_length = sipCpp->size();
        sipBuffer->bd_readonly = 0;
    
        sipRes = 0;
    #else
        sipRes = PyBuffer_FillInfo(sipBuffer, sipSelf, sipCpp->data(),
                sipCpp->size(), 0, sipFlags);
    #endif
%End

%PickleCode
    sipRes = Py_BuildValue("(y#)", sipCpp->data(), static_cast<Py_ssize_t>(sipCpp->size()));
%End

public:
    QByteArray();
    QByteArray(qsizetype size, char c /Encoding="None"/);
    QByteArray(const QByteArray &a);
    ~QByteArray();
    void resize(qsizetype size);
%If (Qt_6_4_0 -)
    void resize(qsizetype size, char c);
%End
    QByteArray &fill(char c /Encoding="None"/, qsizetype size = -1);
    void clear();
    qsizetype indexOf(QByteArrayView bv, qsizetype from = 0) const;
    // In Qt v6.2 this was replaced by two separate overloads.  However we need to keep the optional keyword argument.
    qsizetype lastIndexOf(QByteArrayView bv, qsizetype from = -1) const;
    qsizetype count(QByteArrayView bv) const;
    QByteArray left(qsizetype len) const;
    QByteArray right(qsizetype len) const;
    QByteArray mid(qsizetype index, qsizetype length = -1) const;
    QByteArray first(qsizetype n) const;
    QByteArray last(qsizetype n) const;
    bool startsWith(QByteArrayView bv) const;
    bool endsWith(QByteArrayView bv) const;
    void truncate(qsizetype pos);
    void chop(qsizetype n);
    QByteArray toLower() const;
    QByteArray toUpper() const;
    QByteArray trimmed() const;
    QByteArray simplified() const;
    QByteArray leftJustified(qsizetype width, char fill /Encoding="None"/ = ' ', bool truncate = false) const;
    QByteArray rightJustified(qsizetype width, char fill /Encoding="None"/ = ' ', bool truncate = false) const;
    QByteArray &prepend(QByteArrayView a);
    QByteArray &prepend(qsizetype count, char c /Encoding="None"/);
    QByteArray &append(QByteArrayView a);
    QByteArray &append(qsizetype count, char c /Encoding="None"/);
    QByteArray &insert(qsizetype i, QByteArrayView data);
    QByteArray &insert(qsizetype i, qsizetype count, char c /Encoding="None"/);
    QByteArray &remove(qsizetype index, qsizetype len);
    QByteArray &replace(QByteArrayView before, QByteArrayView after);
    QByteArray &replace(qsizetype index, qsizetype len, QByteArrayView s);
    QList<QByteArray> split(char sep /Encoding="None"/) const;
    QByteArray &operator+=(QByteArrayView a);
    bool operator==(const QString &s2) const;
%MethodCode
        // Needed for Qt v6.8 and later.
        sipRes = (*sipCpp == *a0);
%End

    bool operator!=(const QString &s2) const;
%MethodCode
        // Needed for Qt v6.8 and later.
        sipRes = (*sipCpp != *a0);
%End

    bool operator<(const QString &s2) const;
%MethodCode
        // Needed for Qt v6.8 and later.
        sipRes = (*sipCpp < *a0);
%End

    bool operator>(const QString &s2) const;
%MethodCode
        // Needed for Qt v6.8 and later.
        sipRes = (*sipCpp > *a0);
%End

    bool operator<=(const QString &s2) const;
%MethodCode
        // Needed for Qt v6.8 and later.
        sipRes = (*sipCpp <= *a0);
%End

    bool operator>=(const QString &s2) const;
%MethodCode
        // Needed for Qt v6.8 and later.
        sipRes = (*sipCpp >= *a0);
%End

    short toShort(bool *ok = 0, int base = 10) const;
    ushort toUShort(bool *ok = 0, int base = 10) const;
    int toInt(bool *ok = 0, int base = 10) const;
    uint toUInt(bool *ok = 0, int base = 10) const;
    long toLong(bool *ok = 0, int base = 10) const;
    ulong toULong(bool *ok = 0, int base = 10) const;
    qlonglong toLongLong(bool *ok = 0, int base = 10) const;
    qulonglong toULongLong(bool *ok = 0, int base = 10) const;
    float toFloat(bool *ok = 0) const;
    double toDouble(bool *ok = 0) const;
    QByteArray toBase64(QByteArray::Base64Options options = QByteArray::Base64Encoding) const;
    QByteArray &setNum(double n /Constrained/, char format = 'g', int precision = 6);
    QByteArray &setNum(SIP_PYOBJECT n /TypeHint="int"/, int base = 10);
%MethodCode
        qlonglong val = sipLong_AsLongLong(a0);
        
        if (!PyErr_Occurred())
        {
            sipRes = &sipCpp->setNum(val, a1);
        }
        else
        {
            // If it is positive then it might fit an unsigned long long.
        
            qulonglong uval = sipLong_AsUnsignedLongLong(a0);
        
            if (!PyErr_Occurred())
            {
                sipRes = &sipCpp->setNum(uval, a1);
            }
            else
            {
                sipError = (PyErr_ExceptionMatches(PyExc_OverflowError)
                        ? sipErrorFail : sipErrorContinue);
            }
        }
%End

    static QByteArray number(double n /Constrained/, char format = 'g', int precision = 6);
    static QByteArray number(SIP_PYOBJECT n /TypeHint="int"/, int base = 10);
%MethodCode
        qlonglong val = sipLong_AsLongLong(a0);
        
        if (!PyErr_Occurred())
        {
            sipRes = new QByteArray(QByteArray::number(val, a1));
        }
        else
        {
            // If it is positive then it might fit an unsigned long long.
        
            qulonglong uval = sipLong_AsUnsignedLongLong(a0);
        
            if (!PyErr_Occurred())
            {
                sipRes = new QByteArray(QByteArray::number(uval, a1));
            }
            else
            {
                sipError = (PyErr_ExceptionMatches(PyExc_OverflowError)
                        ? sipErrorFail : sipErrorContinue);
            }
        }
%End

    static QByteArray fromBase64(const QByteArray &base64, QByteArray::Base64Options options = QByteArray::Base64Encoding);
    static QByteArray fromHex(const QByteArray &hexEncoded);
    qsizetype count() const;
    qsizetype length() const;
    bool isNull() const;
    qsizetype size() const /__len__/;
    char at(qsizetype i) const /Encoding="None"/;
    char operator[](qsizetype i) const /Encoding="None"/;
%MethodCode
        Py_ssize_t idx = sipConvertFromSequenceIndex(a0, sipCpp->size());
        
        if (idx < 0)
            sipIsErr = 1;
        else
            sipRes = sipCpp->operator[]((qsizetype)idx);
%End

    QByteArray operator[](SIP_PYSLICE slice) const;
%MethodCode
        Py_ssize_t start, stop, step, slicelength;
        
        if (sipConvertFromSliceObject(a0, sipCpp->size(), &start, &stop, &step, &slicelength) < 0)
        {
            sipIsErr = 1;
        }
        else
        {
            sipRes = new QByteArray();
        
            for (Py_ssize_t i = 0; i < slicelength; ++i)
            {
                sipRes->append(sipCpp->at((qsizetype)start));
                start += step;
            }
        }
%End

    int __contains__(const QByteArrayView bv) const;
%MethodCode
        sipRes = sipCpp->contains(*a0);
%End

    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

    SIP_PYOBJECT __str__() const /TypeHint="str"/;
%MethodCode
        sipRes = QByteArrayToPyStr(sipCpp);
        
        PyObject *repr = PyObject_Repr(sipRes);
        
        if (repr)
        {
            Py_DECREF(sipRes);
            sipRes = repr;
        }
%End

    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        if (sipCpp->isNull())
        {
            sipRes = PyUnicode_FromString("PyQt6.QtCore.QByteArray()");
        }
        else
        {
            PyObject *str = QByteArrayToPyStr(sipCpp);
        
            if (str)
            {
                sipRes = PyUnicode_FromFormat("PyQt6.QtCore.QByteArray(%R)", str);
        
                Py_DECREF(str);
            }
        }
%End

    QByteArray operator*(int m) const;
%MethodCode
        sipRes = new QByteArray();
        
        while (a0-- > 0)
            *sipRes += *sipCpp;
%End

    QByteArray &operator*=(int m);
%MethodCode
        QByteArray orig(*sipCpp);
        
        sipCpp->clear();
        
        while (a0-- > 0)
            *sipCpp += orig;
%End

    bool isEmpty() const;
    SIP_PYOBJECT data() /TypeHint="bytes"/;
%MethodCode
        // QByteArrays may contain embedded '\0's so set the size explicitly.
        
        char *res = sipCpp->data();
        qsizetype len = sipCpp->size();
        
        if (res)
        {
            if ((sipRes = PyBytes_FromStringAndSize(res, (Py_ssize_t)len)) == NULL)
                sipIsErr = 1;
        }
        else
        {
            Py_INCREF(Py_None);
            sipRes = Py_None;
        }
%End

    qsizetype capacity() const;
    void reserve(qsizetype size);
    void squeeze();
    void push_back(QByteArrayView a);
    void push_front(QByteArrayView a);
    bool contains(QByteArrayView bv) const;
    QByteArray toHex(char separator /Encoding="None"/ = '\x00') const;
    QByteArray toPercentEncoding(const QByteArray &exclude = QByteArray(), const QByteArray &include = QByteArray(), char percent = '%') const;
    static QByteArray fromPercentEncoding(const QByteArray &input, char percent = '%');
    QByteArray repeated(qsizetype times) const;
    void swap(QByteArray &other /Constrained/);

    enum Base64Option /BaseType=Flag/
    {
        Base64Encoding,
        Base64UrlEncoding,
        KeepTrailingEquals,
        OmitTrailingEquals,
        IgnoreBase64DecodingErrors,
        AbortOnBase64DecodingErrors,
    };

    typedef QFlags<QByteArray::Base64Option> Base64Options;
    QByteArray chopped(qsizetype len) const;
    int compare(QByteArrayView a, Qt::CaseSensitivity cs = Qt::CaseSensitive) const;
    bool isUpper() const;
    bool isLower() const;

    enum class Base64DecodingStatus
    {
        Ok,
        IllegalInputLength,
        IllegalCharacter,
        IllegalPadding,
    };

    static QByteArray::FromBase64Result fromBase64Encoding(const QByteArray &base64, QByteArray::Base64Options options = QByteArray::Base64Encoding);

    class FromBase64Result
    {
%TypeHeaderCode
#include <qbytearray.h>
%End

    public:
        QByteArray decoded;
        QByteArray::Base64DecodingStatus decodingStatus;
        void swap(QByteArray::FromBase64Result &other /Constrained/);
        operator bool() const;
%MethodCode
            // This is required because SIP doesn't handle operator bool() properly.
            sipRes = sipCpp->operator bool();
%End

        Py_hash_t __hash__() const;
%MethodCode
            sipRes = qHash(*sipCpp);
%End
    };

    QByteArray sliced(qsizetype pos) const;
    QByteArray sliced(qsizetype pos, qsizetype n) const;
%If (Qt_6_3_0 -)
    bool isValidUtf8() const;
%End
%If (Qt_6_4_0 -)
    QByteArray percentDecoded(char percent = '%') const;
%End
%If (Qt_6_5_0 -)
    QByteArray &removeAt(qsizetype pos);
%End
%If (Qt_6_5_0 -)
    QByteArray &removeFirst();
%End
%If (Qt_6_5_0 -)
    QByteArray &removeLast();
%End
%If (Qt_6_6_0 -)
    QByteArray &assign(QByteArrayView v);
%End
%If (Qt_6_8_0 -)
    QByteArray &slice(qsizetype pos, qsizetype n);
%End
%If (Qt_6_8_0 -)
    QByteArray &slice(qsizetype pos);
%End
%If (Qt_6_8_0 -)
    qsizetype max_size() const;
%End
%If (Qt_6_8_0 -)
    static qsizetype maxSize();
%End
};

bool operator==(const QByteArray &a1, const QByteArray &a2);
bool operator!=(const QByteArray &a1, const QByteArray &a2);
bool operator<(const QByteArray &a1, const QByteArray &a2);
bool operator<=(const QByteArray &a1, const QByteArray &a2);
bool operator>(const QByteArray &a1, const QByteArray &a2);
bool operator>=(const QByteArray &a1, const QByteArray &a2);
%If (Qt_6_5_0 -)
QByteArray operator+(const QByteArray &a1, const QByteArray &a2);
%End
%If (- Qt_6_5_0)
const QByteArray operator+(const QByteArray &a1, const QByteArray &a2);
%End
QDataStream &operator<<(QDataStream &, const QByteArray & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QByteArray & /Constrained/) /ReleaseGIL/;
QByteArray qCompress(const uchar *data /Array/, qsizetype nbytes /ArraySize/, int compressionLevel = -1);
QByteArray qCompress(const QByteArray &data, int compressionLevel = -1);
QByteArray qUncompress(const uchar *data /Array/, qsizetype nbytes /ArraySize/);
QByteArray qUncompress(const QByteArray &data);
%If (- Qt_6_2_0)
quint16 qChecksum(QByteArrayView data, Qt::ChecksumType standard = Qt::ChecksumIso3309);
%End
bool operator==(const QByteArray::FromBase64Result &lhs, const QByteArray::FromBase64Result &rhs);
bool operator!=(const QByteArray::FromBase64Result &lhs, const QByteArray::FromBase64Result &rhs);
