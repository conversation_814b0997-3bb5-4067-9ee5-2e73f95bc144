// qqmlapplicationengine.sip generated by MetaSIP
//
// This file is part of the QtQml Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QQmlApplicationEngine : public QQmlEngine
{
%TypeHeaderCode
#include <qqmlapplicationengine.h>
%End

public:
    QQmlApplicationEngine(QObject *parent /TransferThis/ = 0);
    QQmlApplicationEngine(const QUrl &url, QObject *parent /TransferThis/ = 0) /ReleaseGIL/;
    QQmlApplicationEngine(const QString &filePath, QObject *parent /TransferThis/ = 0) /ReleaseGIL/;
%If (Qt_6_5_0 -)
    QQmlApplicationEngine(QAnyStringView uri, QAnyStringView typeName, QObject *parent /TransferThis/ = 0);
%End
    virtual ~QQmlApplicationEngine();
    QList<QObject *> rootObjects() const;

public slots:
    void load(const QUrl &url) /ReleaseGIL/;
    void load(const QString &filePath) /ReleaseGIL/;
    void loadData(const QByteArray &data, const QUrl &url = QUrl()) /ReleaseGIL/;
    void setExtraFileSelectors(const QStringList &extraFileSelectors);
    void setInitialProperties(const QVariantMap &initialProperties);
%If (Qt_6_5_0 -)
    void loadFromModule(QAnyStringView uri, QAnyStringView typeName);
%End

signals:
    void objectCreated(QObject *object, const QUrl &url);
%If (Qt_6_4_0 -)
    void objectCreationFailed(const QUrl &url);
%End
};
