# 文件自动分类工具 2.0 - 部署说明

## 📦 打包完成

### 生成的文件
- **主程序**: `dist/文件自动分类工具2.0.exe` (约30MB)
- **配置文件**: `dist/categories_config.json`
- **界面配置**: `dist/gui_config.json`

### 🎯 功能特性
- ✅ 文章汇总按钮已移至右下角状态栏
- ✅ 完整的GUI界面，包含所有功能模块
- ✅ 独立可执行文件，无需Python环境
- ✅ 包含所有依赖库和资源文件

## 🚀 部署方式

### 方式一：直接运行
1. 将 `dist` 文件夹复制到目标计算机
2. 双击 `文件自动分类工具2.0.exe` 即可运行
3. 首次运行会自动创建必要的目录和配置

### 方式二：安装部署
1. 创建程序目录（如：`C:\Program Files\文件自动分类工具`）
2. 复制所有文件到程序目录
3. 创建桌面快捷方式（可选）

## 🔧 系统要求

### 最低要求
- **操作系统**: Windows 7 SP1 或更高版本
- **内存**: 2GB RAM
- **磁盘空间**: 100MB 可用空间
- **显示器**: 1024x768 分辨率

### 推荐配置
- **操作系统**: Windows 10/11
- **内存**: 4GB RAM 或更高
- **磁盘空间**: 500MB 可用空间
- **显示器**: 1920x1080 分辨率

## 📋 兼容性测试

### 已测试环境
- ✅ Windows 10 (64位)
- ✅ Windows 11 (64位)

### 支持的文件格式
- **文档**: .txt, .doc, .docx, .pdf
- **图片**: .jpg, .png, .gif, .bmp
- **其他**: 支持所有常见文件格式

### 文件保存位置
- **分类文件**: 保存到配置的分类目录（默认：./已分类）
- **汇总文档**: 自动保存到分类目录根目录下，文件名格式：文章汇总_YYYYMMDD_HHMMSS.docx

## 🛠️ 故障排除

### 常见问题

**1. 程序无法启动**
- 检查是否有杀毒软件阻止
- 确保有足够的磁盘空间
- 尝试以管理员身份运行

**2. 配置文件丢失**
- 程序会自动重新创建默认配置
- 可以从源码目录复制配置文件

**3. 文件分类不准确**
- 检查分类规则设置
- 使用"规则管理"功能调整规则

**4. 汇总功能异常**
- 确保有足够的磁盘空间
- 检查目标目录的写入权限

## 📞 技术支持

### 日志文件位置
- 程序运行日志：`logs/file_sorter_gui.log`
- 错误信息会自动记录到日志文件

### 重置程序
如需重置所有设置：
1. 删除 `gui_config.json`
2. 删除 `categories_config.json`
3. 重新启动程序

## 🔄 更新说明

### 版本 2.0 新特性
- 🎨 全新现代化界面设计
- 📄 文章汇总功能（位于添加规则右边）
- ⚙️ 完整的规则管理系统
- 🎯 个性化主题设置
- 🔍 强大的文件搜索和预览
- 📊 实时状态显示和进度跟踪
- ✅ 智能文件选择（默认全选，支持个别勾选/取消）
- 🎯 精准操作（只对勾选文件进行分类和汇总）

### 界面布局
- **左侧**: 目录浏览器
- **中间**: 文件列表和操作（支持个别文件勾选）
- **右侧**: 分类规则配置（文章汇总按钮位于添加规则右边）
- **底部**: 状态栏和选择计数

## 📝 使用提示

1. **首次使用**: 建议先选择一个测试目录熟悉功能
2. **文件选择**: 默认所有文件都被勾选，可以点击复选框取消选择特定文件
3. **精准操作**: 只有被勾选的文件才会参与分类和汇总操作
4. **规则配置**: 可以通过"规则管理"添加自定义分类规则
5. **批量操作**: 支持全选、全不选、反选等批量选择操作
6. **文章汇总**: 分类完成后点击"添加规则"右边的"文章汇总"按钮生成报告（保存到分类目录下）
7. **主题切换**: 通过设置面板可以切换界面主题

---

**打包信息**:
- 打包工具: PyInstaller 6.14.2
- Python版本: 3.11.13
- 打包时间: 2025-07-06
- 文件大小: ~30MB (单文件版本)
