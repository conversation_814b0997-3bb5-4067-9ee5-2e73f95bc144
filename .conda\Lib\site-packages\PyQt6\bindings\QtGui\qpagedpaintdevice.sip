// qpagedpaintdevice.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPagedPaintDevice : public QPaintDevice /NoDefaultCtors/
{
%TypeHeaderCode
#include <qpagedpaintdevice.h>
%End

public:
    virtual ~QPagedPaintDevice();
    virtual bool newPage() = 0;

    enum PdfVersion
    {
        PdfVersion_1_4,
        PdfVersion_A1b,
        PdfVersion_1_6,
%If (Qt_6_8_0 -)
        PdfVersion_X4,
%End
    };

    virtual bool setPageSize(const QPageSize &pageSize);
    virtual bool setPageLayout(const QPageLayout &pageLayout);
    QPageLayout pageLayout() const;
    virtual bool setPageOrientation(QPageLayout::Orientation orientation);
    virtual bool setPageMargins(const QMarginsF &margins, QPageLayout::Unit units = QPageLayout::Millimeter);
    virtual void setPageRanges(const QPageRanges &ranges);
    QPageRanges pageRanges() const;
};
