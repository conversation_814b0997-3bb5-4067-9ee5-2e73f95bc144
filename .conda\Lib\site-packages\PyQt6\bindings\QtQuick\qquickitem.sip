// qquickitem.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QQuickItem : public QObject, public QQmlParserStatus /ExportDerived/
{
%TypeHeaderCode
#include <qquickitem.h>
%End

public:
    enum Flag /BaseType=Flag/
    {
        ItemClipsChildrenToShape,
        ItemAcceptsInputMethod,
        ItemIsFocusScope,
        ItemHasContents,
        ItemAcceptsDrops,
%If (Qt_6_3_0 -)
        ItemIsViewport,
%End
%If (Qt_6_3_0 -)
        ItemObservesViewport,
%End
    };

    typedef QFlags<QQuickItem::Flag> Flags;

    enum ItemChange
    {
        ItemChildAddedChange,
        ItemChildRemovedChange,
        ItemSceneChange,
        ItemVisibleHasChanged,
        ItemParentHasChanged,
        ItemOpacityHasChanged,
        ItemActiveFocusHasChanged,
        ItemRotationHasChanged,
        ItemAntialiasingHasChanged,
        ItemDevicePixelRatioHasChanged,
        ItemEnabledHasChanged,
%If (Qt_6_9_0 -)
        ItemScaleHasChanged,
%End
%If (Qt_6_9_0 -)
        ItemTransformHasChanged,
%End
    };

    struct ItemChangeData
    {
%TypeHeaderCode
#include <qquickitem.h>
%End

        ItemChangeData(QQuickItem *v);
        ItemChangeData(QQuickWindow *v);
        ItemChangeData(qreal v /Constrained/);
        ItemChangeData(bool v /Constrained/);
        QQuickItem *item;
        QQuickWindow *window;
        qreal realValue;
        bool boolValue;
    };

    enum TransformOrigin
    {
        TopLeft,
        Top,
        TopRight,
        Left,
        Center,
        Right,
        BottomLeft,
        Bottom,
        BottomRight,
    };

    explicit QQuickItem(QQuickItem *parent /TransferThis/ = 0);
    virtual ~QQuickItem();
    QQuickWindow *window() const;
    QQuickItem *parentItem() const;
    void setParentItem(QQuickItem *parent);
    void stackBefore(const QQuickItem *);
    void stackAfter(const QQuickItem *);
    QRectF childrenRect();
    QList<QQuickItem *> childItems() const;
    bool clip() const;
    void setClip(bool);
    QString state() const;
    void setState(const QString &);
    qreal baselineOffset() const;
    void setBaselineOffset(qreal);
    qreal x() const;
    qreal y() const;
    void setX(qreal);
    void setY(qreal);
    qreal width() const;
    void setWidth(qreal);
    void resetWidth();
    void setImplicitWidth(qreal);
    qreal implicitWidth() const;
    qreal height() const;
    void setHeight(qreal);
    void resetHeight();
    void setImplicitHeight(qreal);
    qreal implicitHeight() const;
    void setSize(const QSizeF &size);
    QQuickItem::TransformOrigin transformOrigin() const;
    void setTransformOrigin(QQuickItem::TransformOrigin);
    qreal z() const;
    void setZ(qreal);
    qreal rotation() const;
    void setRotation(qreal);
    qreal scale() const;
    void setScale(qreal);
    qreal opacity() const;
    void setOpacity(qreal);
    bool isVisible() const;
    void setVisible(bool);
    bool isEnabled() const;
    void setEnabled(bool);
    bool smooth() const;
    void setSmooth(bool);
    bool antialiasing() const;
    void setAntialiasing(bool);
    QQuickItem::Flags flags() const;
    void setFlag(QQuickItem::Flag flag, bool enabled = true);
    void setFlags(QQuickItem::Flags flags);
    bool hasActiveFocus() const;
    bool hasFocus() const;
    void setFocus(bool);
    bool isFocusScope() const;
    QQuickItem *scopedFocusItem() const;
    Qt::MouseButtons acceptedMouseButtons() const;
    void setAcceptedMouseButtons(Qt::MouseButtons buttons);
    bool acceptHoverEvents() const;
    void setAcceptHoverEvents(bool enabled);
    QCursor cursor() const;
    void setCursor(const QCursor &cursor);
    void unsetCursor();
    void grabMouse();
    void ungrabMouse();
    bool keepMouseGrab() const;
    void setKeepMouseGrab(bool);
    bool filtersChildMouseEvents() const;
    void setFiltersChildMouseEvents(bool filter);
    void grabTouchPoints(const QList<int> &ids);
    void ungrabTouchPoints();
    bool keepTouchGrab() const;
    void setKeepTouchGrab(bool);
    virtual bool contains(const QPointF &point) const;
    QPointF mapToItem(const QQuickItem *item, const QPointF &point) const;
    QPointF mapToScene(const QPointF &point) const;
    QRectF mapRectToItem(const QQuickItem *item, const QRectF &rect) const;
    QRectF mapRectToScene(const QRectF &rect) const;
    QPointF mapFromItem(const QQuickItem *item, const QPointF &point) const;
    QPointF mapFromScene(const QPointF &point) const;
    QRectF mapRectFromItem(const QQuickItem *item, const QRectF &rect) const;
    QRectF mapRectFromScene(const QRectF &rect) const;
    void polish();
    void forceActiveFocus();
    QQuickItem *childAt(qreal x, qreal y) const;
    virtual QVariant inputMethodQuery(Qt::InputMethodQuery query) const;

    struct UpdatePaintNodeData
    {
%TypeHeaderCode
#include <qquickitem.h>
%End

        QSGTransformNode *transformNode;

    private:
        UpdatePaintNodeData();
    };

    virtual bool isTextureProvider() const;
    virtual QSGTextureProvider *textureProvider() const;

public slots:
    void update() /ReleaseGIL/;

signals:
    void childrenRectChanged(const QRectF &);
    void baselineOffsetChanged(qreal);
    void stateChanged(const QString &);
    void focusChanged(bool);
    void activeFocusChanged(bool);
    void parentChanged(QQuickItem *);
    void transformOriginChanged(QQuickItem::TransformOrigin);
    void smoothChanged(bool);
    void antialiasingChanged(bool);
    void clipChanged(bool);
    void opacityChanged();
    void enabledChanged();
    void visibleChanged();
    void rotationChanged();
    void scaleChanged();
    void xChanged();
    void yChanged();
    void widthChanged();
    void heightChanged();
    void zChanged();
    void implicitWidthChanged();
    void implicitHeightChanged();

protected:
    virtual bool event(QEvent *);
    bool isComponentComplete() const;
    virtual void itemChange(QQuickItem::ItemChange, const QQuickItem::ItemChangeData &);
    void updateInputMethod(Qt::InputMethodQueries queries = Qt::ImQueryInput);
    bool widthValid() const;
    bool heightValid() const;
    virtual void classBegin();
    virtual void componentComplete();
    virtual void keyPressEvent(QKeyEvent *event);
    virtual void keyReleaseEvent(QKeyEvent *event);
    virtual void inputMethodEvent(QInputMethodEvent *);
    virtual void focusInEvent(QFocusEvent *);
    virtual void focusOutEvent(QFocusEvent *);
    virtual void mousePressEvent(QMouseEvent *event);
    virtual void mouseMoveEvent(QMouseEvent *event);
    virtual void mouseReleaseEvent(QMouseEvent *event);
    virtual void mouseDoubleClickEvent(QMouseEvent *event);
    virtual void mouseUngrabEvent();
    virtual void touchUngrabEvent();
    virtual void wheelEvent(QWheelEvent *event);
    virtual void touchEvent(QTouchEvent *event);
    virtual void hoverEnterEvent(QHoverEvent *event);
    virtual void hoverMoveEvent(QHoverEvent *event);
    virtual void hoverLeaveEvent(QHoverEvent *event);
    virtual void dragEnterEvent(QDragEnterEvent *);
    virtual void dragMoveEvent(QDragMoveEvent *);
    virtual void dragLeaveEvent(QDragLeaveEvent *);
    virtual void dropEvent(QDropEvent *);
    virtual bool childMouseEventFilter(QQuickItem *, QEvent *);
    virtual void geometryChange(const QRectF &newGeometry, const QRectF &oldGeometry);
    virtual QSGNode *updatePaintNode(QSGNode *, QQuickItem::UpdatePaintNodeData *);
%VirtualCatcherCode
        PyObject *res;
        
        res = sipCallMethod(&sipIsErr, sipMethod, "DD",
                a0, sipType_QSGNode, NULL,
                a1, sipType_QQuickItem_UpdatePaintNodeData, NULL);
        
        if (res)
        {
            sipParseResult(&sipIsErr, sipMethod, res, "H0", sipType_QSGNode, &sipRes);
        
            if (!sipIsErr && sipRes && (sipRes->flags() & QSGNode::OwnedByParent))
                sipTransferTo(res, (PyObject *)sipPySelf);
        
            Py_DECREF(res);
        }
%End

    virtual void releaseResources();
    virtual void updatePolish();

public:
    bool activeFocusOnTab() const;
    void setActiveFocusOnTab(bool);
    void setFocus(bool focus, Qt::FocusReason reason);
    void forceActiveFocus(Qt::FocusReason reason);
    QQuickItem *nextItemInFocusChain(bool forward = true);

signals:
    void activeFocusOnTabChanged(bool);
    void windowChanged(QQuickWindow *window);

public:
    void resetAntialiasing();
    QQuickItemGrabResult *grabToImage(const QSize &targetSize = QSize()) /Factory/;
%MethodCode
        QSharedPointer<QQuickItemGrabResult> *grab;
        
        Py_BEGIN_ALLOW_THREADS
        // This will leak but there seems to be no way to detach the object.
        grab = new QSharedPointer<QQuickItemGrabResult>(sipCpp->grabToImage(*a0));
        Py_END_ALLOW_THREADS
        
        sipRes = grab->data();
%End

    bool isAncestorOf(const QQuickItem *child) const;
    QPointF mapToGlobal(const QPointF &point) const;
    QPointF mapFromGlobal(const QPointF &point) const;
    QSizeF size() const;
    bool acceptTouchEvents() const;
    void setAcceptTouchEvents(bool accept);
    QObject *containmentMask() const;
    void setContainmentMask(QObject *mask /KeepReference/);

signals:
    void containmentMaskChanged();

public:
%If (Qt_6_3_0 -)
    QQuickItem *viewportItem() const;
%End
%If (Qt_6_3_0 -)
    void ensurePolished();
%End
%If (Qt_6_3_0 -)
    void dumpItemTree() const;
%End
%If (Qt_6_7_0 -)
    Qt::FocusPolicy focusPolicy() const;
%End
%If (Qt_6_7_0 -)
    void setFocusPolicy(Qt::FocusPolicy policy);
%End

signals:
%If (Qt_6_7_0 -)
    void focusPolicyChanged(Qt::FocusPolicy);
%End
};
