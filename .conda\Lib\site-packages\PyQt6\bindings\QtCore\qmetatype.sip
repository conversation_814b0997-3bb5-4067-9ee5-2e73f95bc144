// qmetatype.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LIC<PERSON><PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QMetaType
{
%TypeHeaderCode
#include <qmetatype.h>
%End

public:
    enum Type
    {
        UnknownType,
        Void,
        Bool,
        Int,
        UInt,
        LongLong,
        ULongLong,
        Double,
        QChar,
        QVariantMap,
        QVariantList,
        QVariantHash,
        QString,
        QStringList,
        QByteArray,
        QBitArray,
        QDate,
        QTime,
        QDateTime,
        QUrl,
        QLocale,
        QRect,
        QRectF,
        QSize,
        QSizeF,
        QLine,
        QLineF,
        QPoint,
        QPointF,
        LastCoreType,
        FirstGuiType,
        QFont,
        QPixmap,
        QBrush,
        QColor,
        QPalette,
        QIcon,
        QImage,
        QPolygon,
        QRegion,
        QBitmap,
        QCursor,
        QSizePolicy,
        QKeySequence,
        QPen,
        QTextLength,
        QTextFormat,
        QTransform,
        VoidStar,
        Long,
        Short,
        Char,
        Char16,
        Char32,
        ULong,
        UShort,
        UChar,
        Float,
%If (Qt_6_5_0 -)
        Float16,
%End
        QObjectStar,
        QMatrix4x4,
        QVector2D,
        QVector3D,
        QVector4D,
        QQuaternion,
        QEasingCurve,
        QVariant,
        QUuid,
        QModelIndex,
        QPolygonF,
        SChar,
        QRegularExpression,
        QJsonValue,
        QJsonObject,
        QJsonArray,
        QJsonDocument,
        QByteArrayList,
        QPersistentModelIndex,
        QCborSimpleType,
        QCborValue,
        QCborArray,
        QCborMap,
        QColorSpace,
        QVariantPair,
        User,
    };

    static bool isRegistered(int type);
    QMetaType();
    explicit QMetaType(int type);

    enum TypeFlag /BaseType=Flag/
    {
        NeedsConstruction,
        NeedsDestruction,
        PointerToQObject,
        IsEnumeration,
        IsUnsignedEnumeration,
        IsPointer,
        RelocatableType,
        IsQmlList,
%If (Qt_6_2_0 -)
        IsConst,
%End
%If (Qt_6_5_0 -)
        NeedsCopyConstruction,
%End
%If (Qt_6_5_0 -)
        NeedsMoveConstruction,
%End
    };

    typedef QFlags<QMetaType::TypeFlag> TypeFlags;
    qsizetype sizeOf() const;
    QMetaType::TypeFlags flags() const;
    bool isValid() const;
    bool isRegistered() const;
    int id() const;
%If (Qt_6_1_0 -)
    bool hasRegisteredDataStreamOperators() const;
%End
    bool hasRegisteredDebugStreamOperator() const;
    const char *name() const /Encoding="None"/;
    qsizetype alignOf() const;
    bool isEqualityComparable() const;
    bool isOrdered() const;
    static QMetaType fromName(QByteArrayView name);
    static bool canConvert(QMetaType fromType, QMetaType toType);
    static bool canView(QMetaType fromType, QMetaType toType);
%If (Qt_6_4_0 -)
    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

%End
%If (Qt_6_5_0 -)
    void registerType() const;
%End
%If (Qt_6_5_0 -)
    bool isDefaultConstructible() const;
%End
%If (Qt_6_5_0 -)
    bool isCopyConstructible() const;
%End
%If (Qt_6_5_0 -)
    bool isMoveConstructible() const;
%End
%If (Qt_6_5_0 -)
    bool isDestructible() const;
%End
%If (Qt_6_6_0 -)
    QMetaType underlyingType() const;
%End

private:
    QMetaType(const QMetaType &);
};

%If (Qt_6_8_0 -)
bool operator==(const QMetaType &lhs, const QMetaType &rhs);
%End
%If (- Qt_6_8_0)
bool operator==(QMetaType a, QMetaType b);
%End
%If (Qt_6_8_0 -)
bool operator!=(const QMetaType &lhs, const QMetaType &rhs);
%End
%If (- Qt_6_8_0)
bool operator!=(QMetaType a, QMetaType b);
%End
