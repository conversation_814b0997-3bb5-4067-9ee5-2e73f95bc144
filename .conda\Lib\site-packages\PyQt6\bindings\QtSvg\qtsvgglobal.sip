// qtsvgglobal.sip generated by MetaSIP
//
// This file is part of the QtSvg Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_7_0 -)

namespace QtSvg
{
%TypeHeaderCode
#include <qtsvgglobal.h>
%End

    enum Option
    {
        NoOption,
        Tiny12FeaturesOnly,
%If (Qt_6_8_0 -)
        AssumeTrustedSource,
%End
%If (Qt_6_9_0 -)
        DisableSMILAnimations,
%End
%If (Qt_6_9_0 -)
        DisableCSSAnimations,
%End
%If (Qt_6_9_0 -)
        DisableAnimations,
%End
    };

    typedef QFlags<QtSvg::Option> Options;
};

%End
