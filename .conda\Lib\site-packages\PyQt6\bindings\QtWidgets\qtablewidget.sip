// qtablewidget.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QTableWidgetSelectionRange
{
%TypeHeaderCode
#include <qtablewidget.h>
%End

public:
    QTableWidgetSelectionRange();
    QTableWidgetSelectionRange(int top, int left, int bottom, int right);
    int topRow() const;
    int bottomRow() const;
    int leftColumn() const;
    int rightColumn() const;
    int rowCount() const;
    int columnCount() const;
};

class QTableWidgetItem /Supertype=PyQt6.sip.wrapper/
{
%TypeHeaderCode
#include <qtablewidget.h>
%End

public:
    enum ItemType /BaseType=IntEnum/
    {
        Type,
        UserType,
    };

    explicit QTableWidgetItem(int type = QTableWidgetItem::Type);
    QTableWidgetItem(const QString &text, int type = QTableWidgetItem::Type);
    QTableWidgetItem(const QIcon &icon, const QString &text, int type = QTableWidgetItem::Type);
    QTableWidgetItem(const QTableWidgetItem &other);
    virtual ~QTableWidgetItem();
    virtual QTableWidgetItem *clone() const /Factory/;
    QTableWidget *tableWidget() const;
    Qt::ItemFlags flags() const;
    QString text() const;
    QIcon icon() const;
    QString statusTip() const;
    QString toolTip() const;
    QString whatsThis() const;
    QFont font() const;
    int textAlignment() const;
%If (Qt_6_4_0 -)
    void setTextAlignment(Qt::Alignment alignment);
%End
    void setTextAlignment(int alignment);
    Qt::CheckState checkState() const;
    void setCheckState(Qt::CheckState state);
    virtual QVariant data(int role) const;
    virtual void setData(int role, const QVariant &value);
    virtual bool operator<(const QTableWidgetItem &other /NoCopy/) const;
    virtual void read(QDataStream &in) /ReleaseGIL/;
    virtual void write(QDataStream &out) const /ReleaseGIL/;
    int type() const;
    void setFlags(Qt::ItemFlags aflags);
    void setText(const QString &atext);
    void setIcon(const QIcon &aicon);
    void setStatusTip(const QString &astatusTip);
    void setToolTip(const QString &atoolTip);
    void setWhatsThis(const QString &awhatsThis);
    void setFont(const QFont &afont);
    QSize sizeHint() const;
    void setSizeHint(const QSize &size);
    QBrush background() const;
    void setBackground(const QBrush &brush);
    QBrush foreground() const;
    void setForeground(const QBrush &brush);
    int row() const;
    int column() const;
    void setSelected(bool aselect);
    bool isSelected() const;

private:
    QTableWidgetItem &operator=(const QTableWidgetItem &);
};

QDataStream &operator<<(QDataStream &out, const QTableWidgetItem &item) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &in, QTableWidgetItem &item /Constrained/) /ReleaseGIL/;

class QTableWidget : public QTableView
{
%TypeHeaderCode
#include <qtablewidget.h>
%End

public:
    explicit QTableWidget(QWidget *parent /TransferThis/ = 0);
    QTableWidget(int rows, int columns, QWidget *parent /TransferThis/ = 0);
    virtual ~QTableWidget();
    void setRowCount(int rows);
    int rowCount() const;
    void setColumnCount(int columns);
    int columnCount() const;
    int row(const QTableWidgetItem *item) const;
    int column(const QTableWidgetItem *item) const;
    QTableWidgetItem *item(int row, int column) const;
    void setItem(int row, int column, QTableWidgetItem *item /Transfer/);
    QTableWidgetItem *takeItem(int row, int column) /TransferBack/;
    QTableWidgetItem *verticalHeaderItem(int row) const;
    void setVerticalHeaderItem(int row, QTableWidgetItem *item /Transfer/);
    QTableWidgetItem *takeVerticalHeaderItem(int row) /TransferBack/;
    QTableWidgetItem *horizontalHeaderItem(int column) const;
    void setHorizontalHeaderItem(int column, QTableWidgetItem *item /Transfer/);
    QTableWidgetItem *takeHorizontalHeaderItem(int column) /TransferBack/;
    void setVerticalHeaderLabels(const QStringList &labels);
    void setHorizontalHeaderLabels(const QStringList &labels);
    int currentRow() const;
    int currentColumn() const;
    QTableWidgetItem *currentItem() const;
    void setCurrentItem(QTableWidgetItem *item);
    void setCurrentItem(QTableWidgetItem *item, QItemSelectionModel::SelectionFlags command);
    void setCurrentCell(int row, int column);
    void setCurrentCell(int row, int column, QItemSelectionModel::SelectionFlags command);
    void sortItems(int column, Qt::SortOrder order = Qt::AscendingOrder);
    void setSortingEnabled(bool enable);
    bool isSortingEnabled() const;
    void editItem(QTableWidgetItem *item);
    void openPersistentEditor(QTableWidgetItem *item);
    void closePersistentEditor(QTableWidgetItem *item);
    QWidget *cellWidget(int row, int column) const;
    void setCellWidget(int row, int column, QWidget *widget /Transfer/);
%MethodCode
        // We have to break the association with any existing widget.
        QWidget *w = sipCpp->cellWidget(a0, a1);
        
        if (w)
        {
            PyObject *wo = sipGetPyObject(w, sipType_QWidget);
        
            if (wo)
                sipTransferTo(wo, 0);
        }
        
        Py_BEGIN_ALLOW_THREADS
        sipCpp->setCellWidget(a0, a1, a2);
        Py_END_ALLOW_THREADS
%End

    void removeCellWidget(int arow, int acolumn);
%MethodCode
        // We have to break the association with any existing widget.
        QWidget *w = sipCpp->cellWidget(a0, a1);
        
        if (w)
        {
            PyObject *wo = sipGetPyObject(w, sipType_QWidget);
        
            if (wo)
                sipTransferTo(wo, 0);
        }
        
        Py_BEGIN_ALLOW_THREADS
        sipCpp->removeCellWidget(a0, a1);
        Py_END_ALLOW_THREADS
%End

    void setRangeSelected(const QTableWidgetSelectionRange &range, bool select);
    QList<QTableWidgetSelectionRange> selectedRanges() const;
    QList<QTableWidgetItem *> selectedItems() const;
    QList<QTableWidgetItem *> findItems(const QString &text, Qt::MatchFlags flags) const;
    int visualRow(int logicalRow) const;
    int visualColumn(int logicalColumn) const;
    QTableWidgetItem *itemAt(const QPoint &p) const;
    QTableWidgetItem *itemAt(int ax, int ay) const;
    QRect visualItemRect(const QTableWidgetItem *item) const;
    const QTableWidgetItem *itemPrototype() const;
    void setItemPrototype(const QTableWidgetItem *item /Transfer/);

public slots:
    void scrollToItem(const QTableWidgetItem *item, QAbstractItemView::ScrollHint hint = QAbstractItemView::EnsureVisible);
    void insertRow(int row);
    void insertColumn(int column);
    void removeRow(int row);
    void removeColumn(int column);
    void clear();
    void clearContents();

signals:
    void itemPressed(QTableWidgetItem *item);
    void itemClicked(QTableWidgetItem *item);
    void itemDoubleClicked(QTableWidgetItem *item);
    void itemActivated(QTableWidgetItem *item);
    void itemEntered(QTableWidgetItem *item);
    void itemChanged(QTableWidgetItem *item);
    void currentItemChanged(QTableWidgetItem *current, QTableWidgetItem *previous);
    void itemSelectionChanged();
    void cellPressed(int row, int column);
    void cellClicked(int row, int column);
    void cellDoubleClicked(int row, int column);
    void cellActivated(int row, int column);
    void cellEntered(int row, int column);
    void cellChanged(int row, int column);
    void currentCellChanged(int currentRow, int currentColumn, int previousRow, int previousColumn);

protected:
    virtual QStringList mimeTypes() const;
    virtual QMimeData *mimeData(const QList<QTableWidgetItem *> &items) const /TransferBack/;
    virtual bool dropMimeData(int row, int column, const QMimeData *data, Qt::DropAction action);
    virtual Qt::DropActions supportedDropActions() const;

public:
    QList<QTableWidgetItem *> items(const QMimeData *data) const;
    QModelIndex indexFromItem(const QTableWidgetItem *item) const;
    QTableWidgetItem *itemFromIndex(const QModelIndex &index) const;

protected:
    virtual bool event(QEvent *e);
    virtual void dropEvent(QDropEvent *event);

public:
    bool isPersistentEditorOpen(QTableWidgetItem *item) const;

private:
    virtual void setModel(QAbstractItemModel *model /KeepReference/);
};

%If (Qt_6_3_0 -)
bool operator==(const QTableWidgetSelectionRange &lhs, const QTableWidgetSelectionRange &rhs);
%End
%If (Qt_6_3_0 -)
bool operator!=(const QTableWidgetSelectionRange &lhs, const QTableWidgetSelectionRange &rhs);
%End
