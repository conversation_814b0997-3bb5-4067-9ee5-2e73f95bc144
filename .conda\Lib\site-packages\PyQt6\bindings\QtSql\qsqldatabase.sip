// qsqldatabase.sip generated by MetaSIP
//
// This file is part of the QtSql Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSqlDriverCreatorBase /Supertype=PyQt6.sip.wrapper/
{
%TypeHeaderCode
#include <qsqldatabase.h>
%End

public:
    virtual ~QSqlDriverCreatorBase();
    virtual QSqlDriver *createObject() const = 0 /Factory/;
};

class QSqlDatabase
{
%TypeHeaderCode
#include <qsqldatabase.h>
%End

public:
    QSqlDatabase();
    QSqlDatabase(const QSqlDatabase &other);
    ~QSqlDatabase();
    bool open() /ReleaseGIL/;
    bool open(const QString &user, const QString &password) /ReleaseGIL/;
    void close();
    bool isOpen() const;
    bool isOpenError() const;
    QStringList tables(QSql::TableType type = QSql::Tables) const;
    QSqlIndex primaryIndex(const QString &tablename) const;
    QSqlRecord record(const QString &tablename) const;
    QSqlQuery exec(const QString &query = QString()) const /ReleaseGIL/;
    QSqlError lastError() const;
    bool isValid() const;
    bool transaction() /ReleaseGIL/;
    bool commit() /ReleaseGIL/;
    bool rollback() /ReleaseGIL/;
    void setDatabaseName(const QString &name);
    void setUserName(const QString &name);
    void setPassword(const QString &password);
    void setHostName(const QString &host);
    void setPort(int p);
    void setConnectOptions(const QString &options = QString());
    QString databaseName() const;
    QString userName() const;
    QString password() const;
    QString hostName() const;
    QString driverName() const;
    int port() const;
    QString connectOptions() const;
    QString connectionName() const;
    QSqlDriver *driver() const;
    static QSqlDatabase addDatabase(const QString &type, const QString &connectionName = QLatin1String(QSqlDatabase::defaultConnection));
    static QSqlDatabase addDatabase(QSqlDriver *driver, const QString &connectionName = QLatin1String(QSqlDatabase::defaultConnection));
    static QSqlDatabase cloneDatabase(const QSqlDatabase &other, const QString &connectionName);
    static QSqlDatabase cloneDatabase(const QString &other, const QString &connectionName);
    static QSqlDatabase database(const QString &connectionName = QLatin1String(QSqlDatabase::defaultConnection), bool open = true);
    static void removeDatabase(const QString &connectionName);
    static bool contains(const QString &connectionName = QLatin1String(QSqlDatabase::defaultConnection));
    static QStringList drivers();
    static QStringList connectionNames();
    static void registerSqlDriver(const QString &name, QSqlDriverCreatorBase *creator /Transfer/);
    static bool isDriverAvailable(const QString &name);

protected:
    explicit QSqlDatabase(const QString &type);
    explicit QSqlDatabase(QSqlDriver *driver);

public:
    void setNumericalPrecisionPolicy(QSql::NumericalPrecisionPolicy precisionPolicy);
    QSql::NumericalPrecisionPolicy numericalPrecisionPolicy() const;
%If (Qt_6_8_0 -)
    bool moveToThread(QThread *targetThread);
%End
%If (Qt_6_8_0 -)
    QThread *thread() const;
%End
};
