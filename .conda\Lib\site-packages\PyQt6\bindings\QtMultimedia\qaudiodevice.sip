// qaudiodevice.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QAudioDevice
{
%TypeHeaderCode
#include <qaudiodevice.h>
%End

public:
    enum Mode
    {
        Null,
        Input,
        Output,
    };

    QAudioDevice();
    QAudioDevice(const QAudioDevice &other);
    ~QAudioDevice();
    void swap(QAudioDevice &other /Constrained/);
    bool operator==(const QAudioDevice &other) const;
    bool operator!=(const QAudioDevice &other) const;
    bool isNull() const;
    QByteArray id() const;
    QString description() const;
    bool isDefault() const;
    QAudioDevice::Mode mode() const;
    bool isFormatSupported(const QAudioFormat &format) const;
    QAudioFormat preferredFormat() const;
    int minimumSampleRate() const;
    int maximumSampleRate() const;
    int minimumChannelCount() const;
    int maximumChannelCount() const;
    QList<QAudioFormat::SampleFormat> supportedSampleFormats() const;
%If (Qt_6_4_0 -)
    QAudioFormat::ChannelConfig channelConfiguration() const;
%End
};

%End
