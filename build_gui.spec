# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 收集数据文件
datas = []

# 收集docx模板和资源文件
datas += collect_data_files('docx')

# 收集PIL/Pillow数据文件
datas += collect_data_files('PIL')

# 添加配置文件
datas += [('categories_config.json', '.')]
datas += [('gui_config.json', '.')]

# 收集隐藏导入
hiddenimports = []
hiddenimports += collect_submodules('docx')
hiddenimports += collect_submodules('PIL')
hiddenimports += ['tkinter', 'tkinter.ttk', 'tkinter.filedialog', 'tkinter.messagebox']
hiddenimports += ['json', 'os', 'sys', 'datetime', 'logging', 'shutil', 'threading']
hiddenimports += ['docx.shared', 'docx.enum.text', 'docx.oxml.ns']

block_cipher = None

a = Analysis(
    ['gui_new2.0.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='文件自动分类工具2.0',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
    version_file=None,
)
