// qxmlstream.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QXmlStreamAttribute
{
%TypeHeaderCode
#include <qxmlstream.h>
%End

public:
    QXmlStreamAttribute();
    QXmlStreamAttribute(const QString &qualifiedName, const QString &value);
    QXmlStreamAttribute(const QString &namespaceUri, const QString &name, const QString &value);
    QStringView namespaceUri() const;
    QStringView name() const;
    QStringView qualifiedName() const;
    QStringView prefix() const;
    QStringView value() const;
    bool isDefault() const;
%If (- Qt_6_8_0)
    bool operator==(const QXmlStreamAttribute &other) const;
%End
%If (- Qt_6_8_0)
    bool operator!=(const QXmlStreamAttribute &other) const;
%End
};

class QXmlStreamNamespaceDeclaration
{
%TypeHeaderCode
#include <qxmlstream.h>
%End

public:
    QXmlStreamNamespaceDeclaration();
    QXmlStreamNamespaceDeclaration(const QString &prefix, const QString &namespaceUri);
    QStringView prefix() const;
    QStringView namespaceUri() const;
%If (- Qt_6_8_0)
    bool operator==(const QXmlStreamNamespaceDeclaration &other) const;
%End
%If (- Qt_6_8_0)
    bool operator!=(const QXmlStreamNamespaceDeclaration &other) const;
%End
};

typedef QList<QXmlStreamNamespaceDeclaration> QXmlStreamNamespaceDeclarations;

class QXmlStreamNotationDeclaration
{
%TypeHeaderCode
#include <qxmlstream.h>
%End

public:
    QXmlStreamNotationDeclaration();
    QStringView name() const;
    QStringView systemId() const;
    QStringView publicId() const;
%If (- Qt_6_8_0)
    bool operator==(const QXmlStreamNotationDeclaration &other) const;
%End
%If (- Qt_6_8_0)
    bool operator!=(const QXmlStreamNotationDeclaration &other) const;
%End
};

typedef QList<QXmlStreamNotationDeclaration> QXmlStreamNotationDeclarations;

class QXmlStreamEntityDeclaration
{
%TypeHeaderCode
#include <qxmlstream.h>
%End

public:
    QXmlStreamEntityDeclaration();
    QStringView name() const;
    QStringView notationName() const;
    QStringView systemId() const;
    QStringView publicId() const;
    QStringView value() const;
%If (- Qt_6_8_0)
    bool operator==(const QXmlStreamEntityDeclaration &other) const;
%End
%If (- Qt_6_8_0)
    bool operator!=(const QXmlStreamEntityDeclaration &other) const;
%End
};

typedef QList<QXmlStreamEntityDeclaration> QXmlStreamEntityDeclarations;

class QXmlStreamEntityResolver
{
%TypeHeaderCode
#include <qxmlstream.h>
%End

public:
%If (Qt_6_8_0 -)
    QXmlStreamEntityResolver();
%End
    virtual ~QXmlStreamEntityResolver();
    virtual QString resolveUndeclaredEntity(const QString &name);

private:
%If (Qt_6_8_0 -)
    QXmlStreamEntityResolver(const QXmlStreamEntityResolver &);
%End
};

class QXmlStreamReader
{
%TypeHeaderCode
#include <qxmlstream.h>
%End

public:
    enum TokenType
    {
        NoToken,
        Invalid,
        StartDocument,
        EndDocument,
        StartElement,
        EndElement,
        Characters,
        Comment,
        DTD,
        EntityReference,
        ProcessingInstruction,
    };

    QXmlStreamReader();
    explicit QXmlStreamReader(QIODevice *device);
%If (Qt_6_5_0 -)
    explicit QXmlStreamReader(QAnyStringView data);
%End
%If (- Qt_6_5_0)
    explicit QXmlStreamReader(const QByteArray &data);
%End
%If (- Qt_6_5_0)
    explicit QXmlStreamReader(const QString &data);
%End
    ~QXmlStreamReader();
    void setDevice(QIODevice *device);
    QIODevice *device() const;
%If (Qt_6_5_0 -)
    void addData(QAnyStringView data);
%End
%If (- Qt_6_5_0)
    void addData(const QByteArray &data);
%End
%If (- Qt_6_5_0)
    void addData(const QString &data);
%End
    void clear();
    bool atEnd() const;
    QXmlStreamReader::TokenType readNext();
    QXmlStreamReader::TokenType tokenType() const;
    QString tokenString() const;
    void setNamespaceProcessing(bool);
    bool namespaceProcessing() const;
    bool isStartDocument() const;
    bool isEndDocument() const;
    bool isStartElement() const;
    bool isEndElement() const;
    bool isCharacters() const;
    bool isWhitespace() const;
    bool isCDATA() const;
    bool isComment() const;
    bool isDTD() const;
    bool isEntityReference() const;
    bool isProcessingInstruction() const;
    bool isStandaloneDocument() const;
    QStringView documentVersion() const;
    QStringView documentEncoding() const;
    qint64 lineNumber() const;
    qint64 columnNumber() const;
    qint64 characterOffset() const;
    QXmlStreamAttributes attributes() const;

    enum ReadElementTextBehaviour
    {
        ErrorOnUnexpectedElement,
        IncludeChildElements,
        SkipChildElements,
    };

    QString readElementText(QXmlStreamReader::ReadElementTextBehaviour behaviour = QXmlStreamReader::ErrorOnUnexpectedElement);
    QStringView name() const;
    QStringView namespaceUri() const;
    QStringView qualifiedName() const;
    QStringView prefix() const;
    QStringView processingInstructionTarget() const;
    QStringView processingInstructionData() const;
    QStringView text() const;
    QXmlStreamNamespaceDeclarations namespaceDeclarations() const;
    void addExtraNamespaceDeclaration(const QXmlStreamNamespaceDeclaration &extraNamespaceDeclaraction);
    void addExtraNamespaceDeclarations(const QXmlStreamNamespaceDeclarations &extraNamespaceDeclaractions);
    QXmlStreamNotationDeclarations notationDeclarations() const;
    QXmlStreamEntityDeclarations entityDeclarations() const;
    QStringView dtdName() const;
    QStringView dtdPublicId() const;
    QStringView dtdSystemId() const;

    enum Error
    {
        NoError,
        UnexpectedElementError,
        CustomError,
        NotWellFormedError,
        PrematureEndOfDocumentError,
    };

    void raiseError(const QString &message = QString());
    QString errorString() const;
    QXmlStreamReader::Error error() const;
    bool hasError() const;
    void setEntityResolver(QXmlStreamEntityResolver *resolver /KeepReference/);
    QXmlStreamEntityResolver *entityResolver() const;
    bool readNextStartElement();
    void skipCurrentElement();
    int entityExpansionLimit() const;
    void setEntityExpansionLimit(int limit);
%If (Qt_6_6_0 -)
    bool hasStandaloneDeclaration() const;
%End

private:
    QXmlStreamReader(const QXmlStreamReader &);
};

class QXmlStreamWriter
{
%TypeHeaderCode
#include <qxmlstream.h>
%End

public:
    QXmlStreamWriter();
    explicit QXmlStreamWriter(QIODevice *device);
    explicit QXmlStreamWriter(QByteArray *array);
    ~QXmlStreamWriter();
    void setDevice(QIODevice *device);
    QIODevice *device() const;
    void setAutoFormatting(bool);
    bool autoFormatting() const;
    void setAutoFormattingIndent(int spaces);
    int autoFormattingIndent() const;
%If (Qt_6_5_0 -)
    void writeAttribute(QAnyStringView namespaceUri, QAnyStringView name, QAnyStringView value);
%End
%If (Qt_6_5_0 -)
    void writeAttribute(QAnyStringView qualifiedName, QAnyStringView value);
%End
%If (- Qt_6_5_0)
    void writeAttribute(const QString &qualifiedName, const QString &value);
%End
%If (- Qt_6_5_0)
    void writeAttribute(const QString &namespaceUri, const QString &name, const QString &value);
%End
    void writeAttribute(const QXmlStreamAttribute &attribute);
    void writeAttributes(const QXmlStreamAttributes &attributes);
%If (Qt_6_5_0 -)
    void writeCDATA(QAnyStringView text);
%End
%If (- Qt_6_5_0)
    void writeCDATA(const QString &text);
%End
%If (Qt_6_5_0 -)
    void writeCharacters(QAnyStringView text);
%End
%If (- Qt_6_5_0)
    void writeCharacters(const QString &text);
%End
%If (Qt_6_5_0 -)
    void writeComment(QAnyStringView text);
%End
%If (- Qt_6_5_0)
    void writeComment(const QString &text);
%End
%If (Qt_6_5_0 -)
    void writeDTD(QAnyStringView dtd);
%End
%If (- Qt_6_5_0)
    void writeDTD(const QString &dtd);
%End
%If (Qt_6_5_0 -)
    void writeEmptyElement(QAnyStringView namespaceUri, QAnyStringView name);
%End
%If (Qt_6_5_0 -)
    void writeEmptyElement(QAnyStringView qualifiedName);
%End
%If (- Qt_6_5_0)
    void writeEmptyElement(const QString &qualifiedName);
%End
%If (- Qt_6_5_0)
    void writeEmptyElement(const QString &namespaceUri, const QString &name);
%End
%If (Qt_6_5_0 -)
    void writeTextElement(QAnyStringView namespaceUri, QAnyStringView name, QAnyStringView text);
%End
%If (Qt_6_5_0 -)
    void writeTextElement(QAnyStringView qualifiedName, QAnyStringView text);
%End
%If (- Qt_6_5_0)
    void writeTextElement(const QString &qualifiedName, const QString &text);
%End
%If (- Qt_6_5_0)
    void writeTextElement(const QString &namespaceUri, const QString &name, const QString &text);
%End
    void writeEndDocument();
    void writeEndElement();
%If (Qt_6_5_0 -)
    void writeEntityReference(QAnyStringView name);
%End
%If (- Qt_6_5_0)
    void writeEntityReference(const QString &name);
%End
%If (Qt_6_5_0 -)
    void writeNamespace(QAnyStringView namespaceUri, QAnyStringView prefix = {});
%End
%If (- Qt_6_5_0)
    void writeNamespace(const QString &namespaceUri, const QString &prefix = QString());
%End
%If (Qt_6_5_0 -)
    void writeDefaultNamespace(QAnyStringView namespaceUri);
%End
%If (- Qt_6_5_0)
    void writeDefaultNamespace(const QString &namespaceUri);
%End
%If (Qt_6_5_0 -)
    void writeProcessingInstruction(QAnyStringView target, QAnyStringView data = {});
%End
%If (- Qt_6_5_0)
    void writeProcessingInstruction(const QString &target, const QString &data = QString());
%End
%If (Qt_6_5_0 -)
    void writeStartDocument(QAnyStringView version, bool standalone);
%End
%If (Qt_6_5_0 -)
    void writeStartDocument(QAnyStringView version);
%End
    void writeStartDocument();
%If (- Qt_6_5_0)
    void writeStartDocument(const QString &version);
%End
%If (- Qt_6_5_0)
    void writeStartDocument(const QString &version, bool standalone);
%End
%If (Qt_6_5_0 -)
    void writeStartElement(QAnyStringView namespaceUri, QAnyStringView name);
%End
%If (Qt_6_5_0 -)
    void writeStartElement(QAnyStringView qualifiedName);
%End
%If (- Qt_6_5_0)
    void writeStartElement(const QString &qualifiedName);
%End
%If (- Qt_6_5_0)
    void writeStartElement(const QString &namespaceUri, const QString &name);
%End
    void writeCurrentToken(const QXmlStreamReader &reader);
    bool hasError() const;

private:
    QXmlStreamWriter(const QXmlStreamWriter &);
};

class QXmlStreamAttributes
{
%TypeHeaderCode
#include <qxmlstream.h>
%End

public:
    QXmlStreamAttributes();
%If (Qt_6_6_0 -)
    QStringView value(QAnyStringView namespaceUri, QAnyStringView name) const;
%End
%If (- Qt_6_6_0)
    QStringView value(const QString &namespaceUri, const QString &name) const;
%End
%If (Qt_6_6_0 -)
    QStringView value(QAnyStringView qualifiedName) const;
%End
%If (- Qt_6_6_0)
    QStringView value(const QString &qualifiedName) const;
%End
    void append(const QString &namespaceUri, const QString &name, const QString &value);
    void append(const QString &qualifiedName, const QString &value);
%If (Qt_6_6_0 -)
    bool hasAttribute(QAnyStringView namespaceUri, QAnyStringView name) const;
%End
%If (- Qt_6_6_0)
    bool hasAttribute(const QString &namespaceUri, const QString &name) const;
%End
%If (Qt_6_6_0 -)
    bool hasAttribute(QAnyStringView qualifiedName) const;
%End
%If (- Qt_6_6_0)
    bool hasAttribute(const QString &qualifiedName) const;
%End
// Methods inherited from QList<QXmlStreamAttribute> and Python special methods.
// Keep in sync with QPolygon and QPolygonF.


void append(const QXmlStreamAttribute &value);

const QXmlStreamAttribute &at(int i) const;
void clear();
bool contains(const QXmlStreamAttribute &value) const;
int count(const QXmlStreamAttribute &value) const;
int count() const /__len__/;
void *data();

// Note the Qt return value is discarded as it would require handwritten code
// and seems pretty useless.
void fill(const QXmlStreamAttribute &value, int size = -1);

QXmlStreamAttribute &first();
int indexOf(const QXmlStreamAttribute &value, int from = 0) const;
void insert(int i, const QXmlStreamAttribute &value);
bool isEmpty() const;
QXmlStreamAttribute &last();
int lastIndexOf(const QXmlStreamAttribute &value, int from = -1) const;

// Note the Qt return type is QList<QXmlStreamAttribute>.  We can't do the
// usual trick because there is no QXmlStreamAttributes ctor that takes a
// QList<QXmlStreamAttribute> argument.  We could use handwritten code but we
// don't bother.
//QXmlStreamAttributes mid(int pos, int length = -1) const;

void prepend(const QXmlStreamAttribute &value);
void remove(int i);
void remove(int i, int count);
void resize(qsizetype size);
void replace(int i, const QXmlStreamAttribute &value);
int size() const;

// These are hidden by other implementations in QXmlStreamAttributes.
//QXmlStreamAttribute value(int i) const;
//QXmlStreamAttribute value(int i, const QXmlStreamAttribute &defaultValue) const;

bool operator!=(const QXmlStreamAttributes &other) const;

// Note the Qt return type is QList<QXmlStreamAttribute>.  We can't do the
// usual trick because there is no QXmlStreamAttributes ctor that takes a
// QList<QXmlStreamAttribute> argument.  We could use handwritten code but we
// don't bother.
//QXmlStreamAttributes operator+(const QXmlStreamAttributes &other) const;

QXmlStreamAttributes &operator+=(const QXmlStreamAttributes &other);
QXmlStreamAttributes &operator+=(const QXmlStreamAttribute &value);

bool operator==(const QXmlStreamAttributes &other) const;

QXmlStreamAttribute &operator[](int i);
%MethodCode
Py_ssize_t idx = sipConvertFromSequenceIndex(a0, sipCpp->count());

if (idx < 0)
    sipIsErr = 1;
else
    sipRes = &sipCpp->operator[]((int)idx);
%End

// Some additional Python special methods.

void __setitem__(int i, const QXmlStreamAttribute &value);
%MethodCode
int len;

len = sipCpp->count();

if ((a0 = (int)sipConvertFromSequenceIndex(a0, len)) < 0)
    sipIsErr = 1;
else
    (*sipCpp)[a0] = *a1;
%End

void __setitem__(SIP_PYSLICE slice, const QXmlStreamAttributes &list);
%MethodCode
Py_ssize_t start, stop, step, slicelength;

if (sipConvertFromSliceObject(a0, sipCpp->count(), &start, &stop, &step, &slicelength) < 0)
{
    sipIsErr = 1;
}
else
{
    int vlen = a1->count();

    if (vlen != slicelength)
    {
        sipBadLengthForSlice(vlen, slicelength);
        sipIsErr = 1;
    }
    else
    {
        QList<QXmlStreamAttribute>::const_iterator it = a1->begin();

        for (Py_ssize_t i = 0; i < slicelength; ++i)
        {
            (*sipCpp)[start] = *it;
            start += step;
            ++it;
        }
    }
}
%End

void __delitem__(int i);
%MethodCode
if ((a0 = (int)sipConvertFromSequenceIndex(a0, sipCpp->count())) < 0)
    sipIsErr = 1;
else
    sipCpp->remove(a0);
%End

void __delitem__(SIP_PYSLICE slice);
%MethodCode
Py_ssize_t start, stop, step, slicelength;

if (sipConvertFromSliceObject(a0, sipCpp->count(), &start, &stop, &step, &slicelength) < 0)
{
    sipIsErr = 1;
}
else
{
    for (Py_ssize_t i = 0; i < slicelength; ++i)
    {
        sipCpp->remove(start);
        start += step - 1;
    }
}
%End

QXmlStreamAttributes operator[](SIP_PYSLICE slice);
%MethodCode
Py_ssize_t start, stop, step, slicelength;

if (sipConvertFromSliceObject(a0, sipCpp->count(), &start, &stop, &step, &slicelength) < 0)
{
    sipIsErr = 1;
}
else
{
    sipRes = new QXmlStreamAttributes();

    for (Py_ssize_t i = 0; i < slicelength; ++i)
    {
        (*sipRes) += (*sipCpp)[start];
        start += step;
    }
}
%End

int __contains__(const QXmlStreamAttribute &value);
%MethodCode
// It looks like you can't assign QBool to int.
sipRes = bool(sipCpp->contains(*a0));
%End
};

%If (Qt_6_8_0 -)
bool operator!=(const QXmlStreamEntityDeclaration &lhs, const QXmlStreamEntityDeclaration &rhs);
%End
%If (Qt_6_8_0 -)
bool operator!=(const QXmlStreamNotationDeclaration &lhs, const QXmlStreamNotationDeclaration &rhs);
%End
%If (Qt_6_8_0 -)
bool operator!=(const QXmlStreamNamespaceDeclaration &lhs, const QXmlStreamNamespaceDeclaration &rhs);
%End
%If (Qt_6_8_0 -)
bool operator!=(const QXmlStreamAttribute &lhs, const QXmlStreamAttribute &rhs);
%End
%If (Qt_6_8_0 -)
bool operator==(const QXmlStreamEntityDeclaration &lhs, const QXmlStreamEntityDeclaration &rhs);
%End
%If (Qt_6_8_0 -)
bool operator==(const QXmlStreamNotationDeclaration &lhs, const QXmlStreamNotationDeclaration &rhs);
%End
%If (Qt_6_8_0 -)
bool operator==(const QXmlStreamNamespaceDeclaration &lhs, const QXmlStreamNamespaceDeclaration &rhs);
%End
%If (Qt_6_8_0 -)
bool operator==(const QXmlStreamAttribute &lhs, const QXmlStreamAttribute &rhs);
%End
