"""
文件自动分类工具 GUI 3.0版本
基于PyQt6框架的现代化界面设计
优化代码结构，提升性能，保持100%功能兼容
"""

import sys
import os
import json
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

# PyQt6 imports
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QGridLayout, QSplitter, QFrame, QLabel, QPushButton, QLineEdit,
    QTreeWidget, QTreeWidgetItem, QTableWidget, QTableWidgetItem,
    QCheckBox, QComboBox, QSpinBox, QTextEdit, QTabWidget,
    QProgressBar, QStatusBar, QMenuBar, QMenu, QFileDialog,
    QMessageBox, QDialog, QDialogButtonBox, QGroupBox,
    QRadioButton, QButtonGroup, QScrollArea, QHeaderView
)
from PyQt6.QtCore import (
    Qt, QTimer, QThread, pyqtSignal, QPropertyAnimation, QEasingCurve,
    QRect, QSize, QPoint, QSettings, QStandardPaths
)
from PyQt6.QtGui import (
    QFont, QColor, QPalette, QIcon, QPixmap, QPainter, QBrush,
    QLinearGradient, QAction, QKeySequence
)

# 导入核心模块
from core.file_processor import FileProcessor
from core.config_manager import ConfigManager as CoreConfigManager
from core.document_generator import DocumentGenerator
from core.exceptions import FileSorterError
from core.logger import setup_module_logger


class ModernTheme:
    """现代化主题系统 - PyQt6版本"""
    
    # 当前主题配置
    current_theme = "默认蓝色"
    current_font_size = "中等"
    
    # 主题配色方案
    THEME_COLORS = {
        "默认蓝色": {
            'primary': '#4CAFFE',
            'primary_dark': '#2E8EE0',
            'primary_light': '#5BB6FF',
            'accent': '#FF6B6B',
            'accent_hover': '#FF5252',
        },
        "商务灰色": {
            'primary': '#607D8B',
            'primary_dark': '#455A64',
            'primary_light': '#546E7A',
            'accent': '#FF9800',
            'accent_hover': '#F57C00',
        },
        "护眼绿色": {
            'primary': '#4CAF50',
            'primary_dark': '#388E3C',
            'primary_light': '#43A047',
            'accent': '#FF5722',
            'accent_hover': '#E64A19',
        },
        "温暖橙色": {
            'primary': '#FF9800',
            'primary_dark': '#F57C00',
            'primary_light': '#FB8C00',
            'accent': '#2196F3',
            'accent_hover': '#1976D2',
        }
    }
    
    # 基础颜色系统
    COLORS = {
        # 背景色
        'background': '#FFFFFF',
        'surface': '#F9FBFC',
        'card': '#FFFFFF',
        'nav_bg': '#F2F7FA',
        
        # 文字色
        'text_primary': '#333333',
        'text_secondary': '#666666',
        'text_muted': '#999999',
        'text_white': '#FFFFFF',
        
        # 状态色
        'success': '#4CAF50',
        'warning': '#FF9800',
        'error': '#F44336',
        'info': '#2196F3',
        
        # 边框和分隔线
        'border': '#E0E0E0',
        'divider': '#F0F0F0',
        
        # 交互色
        'hover': '#F5F8FE',
        'selected': '#E3F2FD',
        'focus': '#2E8EE0',
    }
    
    # 字体配置
    FONT_SIZES = {
        "小": {'base': 9, 'title': 12, 'subtitle': 10},
        "中等": {'base': 10, 'title': 14, 'subtitle': 12},
        "大": {'base': 12, 'title': 16, 'subtitle': 14},
        "特大": {'base': 14, 'title': 18, 'subtitle': 16},
    }
    
    # 尺寸配置
    SIZES = {
        'border_radius': 6,
        'button_height': 36,
        'input_height': 32,
        'spacing_xs': 4,
        'spacing_sm': 8,
        'spacing_md': 12,
        'spacing_lg': 16,
        'spacing_xl': 24,
        'icon_size': 16,
        'toolbar_height': 60,
        'statusbar_height': 30,
    }
    
    @classmethod
    def apply_theme(cls, theme_name: str):
        """应用主题"""
        if theme_name in cls.THEME_COLORS:
            cls.current_theme = theme_name
            theme_colors = cls.THEME_COLORS[theme_name]
            cls.COLORS.update({
                'primary': theme_colors['primary'],
                'primary_dark': theme_colors['primary_dark'],
                'primary_light': theme_colors['primary_light'],
                'accent': theme_colors['accent'],
                'accent_hover': theme_colors['accent_hover'],
            })
    
    @classmethod
    def apply_font_size(cls, size_name: str):
        """应用字体大小"""
        if size_name in cls.FONT_SIZES:
            cls.current_font_size = size_name
    
    @classmethod
    def get_font(cls, font_type: str = 'base') -> QFont:
        """获取字体"""
        size_config = cls.FONT_SIZES[cls.current_font_size]
        size = size_config.get(font_type, size_config['base'])
        font = QFont("Microsoft YaHei", size)
        if font_type == 'title':
            font.setBold(True)
        return font
    
    @classmethod
    def get_color(cls, color_name: str) -> QColor:
        """获取颜色"""
        color_value = cls.COLORS.get(color_name, '#000000')
        return QColor(color_value)
    
    @classmethod
    def get_stylesheet(cls) -> str:
        """获取全局样式表"""
        return f"""
        QMainWindow {{
            background-color: {cls.COLORS['background']};
            color: {cls.COLORS['text_primary']};
        }}
        
        QFrame {{
            background-color: {cls.COLORS['surface']};
            border: none;
        }}
        
        QPushButton {{
            background-color: {cls.COLORS['primary']};
            color: {cls.COLORS['text_white']};
            border: none;
            border-radius: {cls.SIZES['border_radius']}px;
            padding: 8px 16px;
            font-weight: bold;
        }}
        
        QPushButton:hover {{
            background-color: {cls.COLORS['primary_light']};
        }}
        
        QPushButton:pressed {{
            background-color: {cls.COLORS['primary_dark']};
        }}
        
        QPushButton:disabled {{
            background-color: {cls.COLORS['border']};
            color: {cls.COLORS['text_muted']};
        }}
        
        QLineEdit {{
            background-color: {cls.COLORS['card']};
            border: 1px solid {cls.COLORS['border']};
            border-radius: {cls.SIZES['border_radius']}px;
            padding: 6px 12px;
            font-size: {cls.FONT_SIZES[cls.current_font_size]['base']}px;
        }}
        
        QLineEdit:focus {{
            border-color: {cls.COLORS['focus']};
        }}
        
        QTreeWidget, QTableWidget {{
            background-color: {cls.COLORS['card']};
            border: 1px solid {cls.COLORS['border']};
            border-radius: {cls.SIZES['border_radius']}px;
            gridline-color: {cls.COLORS['divider']};
        }}
        
        QTreeWidget::item:selected, QTableWidget::item:selected {{
            background-color: {cls.COLORS['selected']};
        }}
        
        QTreeWidget::item:hover, QTableWidget::item:hover {{
            background-color: {cls.COLORS['hover']};
        }}
        
        QHeaderView::section {{
            background-color: {cls.COLORS['nav_bg']};
            border: none;
            border-bottom: 1px solid {cls.COLORS['border']};
            padding: 8px;
            font-weight: bold;
        }}
        
        QTabWidget::pane {{
            border: 1px solid {cls.COLORS['border']};
            border-radius: {cls.SIZES['border_radius']}px;
        }}
        
        QTabBar::tab {{
            background-color: {cls.COLORS['surface']};
            border: 1px solid {cls.COLORS['border']};
            padding: 8px 16px;
            margin-right: 2px;
        }}
        
        QTabBar::tab:selected {{
            background-color: {cls.COLORS['primary']};
            color: {cls.COLORS['text_white']};
        }}
        
        QStatusBar {{
            background-color: {cls.COLORS['nav_bg']};
            border-top: 1px solid {cls.COLORS['border']};
        }}
        """


class ConfigManager:
    """配置管理器 - PyQt6版本"""
    
    def __init__(self):
        self.core_config = CoreConfigManager()
        self.settings = QSettings('FileSorter', 'GUI3.0')
        self.gui_config = self._load_gui_config()
    
    def _load_gui_config(self) -> Dict[str, Any]:
        """加载GUI配置"""
        default_config = {
            'target_path': './已分类',
            'auto_refresh': True,
            'show_notifications': True,
            'font_size': '中等',
            'color_scheme': '默认蓝色',
            'batch_size': 100,
            'enable_logging': True,
            'window_geometry': None,
            'window_state': None,
            'splitter_state': None,
        }
        
        # 从QSettings加载配置
        for key, default_value in default_config.items():
            value = self.settings.value(key, default_value)
            # 处理布尔值
            if isinstance(default_value, bool):
                value = value if isinstance(value, bool) else value.lower() == 'true'
            # 处理整数值
            elif isinstance(default_value, int):
                value = int(value) if value else default_value
            default_config[key] = value
        
        return default_config
    
    def get_config(self) -> Dict[str, Any]:
        """获取完整配置"""
        return self.gui_config.copy()
    
    def update_config(self, new_config: Dict[str, Any]):
        """更新配置"""
        self.gui_config.update(new_config)
        self._save_gui_config()
    
    def _save_gui_config(self):
        """保存GUI配置"""
        for key, value in self.gui_config.items():
            self.settings.setValue(key, value)
        self.settings.sync()
    
    # 代理核心配置管理器的方法
    def get_categories(self):
        return self.core_config.get_categories()
    
    def add_category(self, name, keywords, match_type='contains', priority=1):
        return self.core_config.add_category(name, keywords, match_type, priority)
    
    def update_category(self, old_name, new_name, keywords, match_type='contains', priority=1):
        return self.core_config.update_category(old_name, new_name, keywords, match_type, priority)
    
    def delete_category(self, name):
        return self.core_config.delete_category(name)
    
    def get_category_by_name(self, name):
        return self.core_config.get_category_by_name(name)
    
    def get_category_for_filename(self, filename):
        return self.core_config.get_category_for_filename(filename)


class ModernCard(QFrame):
    """现代化卡片组件 - PyQt6版本"""

    def __init__(self, title: str = "", parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.title = title
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        self.setFrameStyle(QFrame.Shape.Box)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {ModernTheme.COLORS['card']};
                border: 1px solid {ModernTheme.COLORS['border']};
                border-radius: {ModernTheme.SIZES['border_radius']}px;
            }}
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(ModernTheme.SIZES['spacing_md'],
                                 ModernTheme.SIZES['spacing_md'],
                                 ModernTheme.SIZES['spacing_md'],
                                 ModernTheme.SIZES['spacing_md'])

        if self.title:
            title_label = QLabel(self.title)
            title_label.setFont(ModernTheme.get_font('title'))
            title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            title_label.setStyleSheet(f"color: {ModernTheme.COLORS['text_primary']}; border: none;")
            layout.addWidget(title_label)

        layout.addWidget(self.content_widget)

    def get_content_layout(self) -> QVBoxLayout:
        """获取内容布局"""
        return self.content_layout


class ModernButton(QPushButton):
    """现代化按钮组件 - PyQt6版本"""

    def __init__(self, text: str = "", icon: str = "", style_type: str = "primary",
                 parent: Optional[QWidget] = None):
        display_text = f"{icon} {text}" if icon else text
        super().__init__(display_text, parent)
        self.style_type = style_type
        self.icon_text = icon
        self.original_text = text
        self.is_loading = False
        self.setup_style()

    def setup_style(self):
        """设置按钮样式"""
        if self.style_type == "primary":
            bg_color = ModernTheme.COLORS['primary']
            hover_color = ModernTheme.COLORS['primary_light']
            text_color = ModernTheme.COLORS['text_white']
        elif self.style_type == "secondary":
            bg_color = ModernTheme.COLORS['surface']
            hover_color = ModernTheme.COLORS['hover']
            text_color = ModernTheme.COLORS['text_primary']
        elif self.style_type == "accent":
            bg_color = ModernTheme.COLORS['accent']
            hover_color = ModernTheme.COLORS['accent_hover']
            text_color = ModernTheme.COLORS['text_white']
        else:
            bg_color = ModernTheme.COLORS['surface']
            hover_color = ModernTheme.COLORS['hover']
            text_color = ModernTheme.COLORS['text_primary']

        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                color: {text_color};
                border: none;
                border-radius: {ModernTheme.SIZES['border_radius']}px;
                padding: 8px 16px;
                font-weight: bold;
                min-height: {ModernTheme.SIZES['button_height'] - 16}px;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
            QPushButton:pressed {{
                background-color: {bg_color};
                transform: translateY(1px);
            }}
            QPushButton:disabled {{
                background-color: {ModernTheme.COLORS['border']};
                color: {ModernTheme.COLORS['text_muted']};
            }}
        """)

        self.setFont(ModernTheme.get_font('base'))

    def set_loading(self, loading: bool):
        """设置加载状态"""
        self.is_loading = loading
        if loading:
            self.setText("⟳ 处理中...")
            self.setEnabled(False)
        else:
            display_text = f"{self.icon_text} {self.original_text}" if self.icon_text else self.original_text
            self.setText(display_text)
            self.setEnabled(True)


class ToastNotification(QWidget):
    """Toast通知组件 - PyQt6版本"""

    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.parent_widget = parent
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setup_ui()
        self.animation = QPropertyAnimation(self, b"windowOpacity")
        self.timer = QTimer()
        self.timer.timeout.connect(self.hide_toast)

    def setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(16, 12, 16, 12)

        self.icon_label = QLabel()
        self.message_label = QLabel()
        self.message_label.setFont(ModernTheme.get_font('base'))

        layout.addWidget(self.icon_label)
        layout.addWidget(self.message_label)

        self.setFixedHeight(50)

    def show_toast(self, message: str, toast_type: str = "info", duration: int = 3000):
        """显示Toast通知"""
        # 设置图标和样式
        if toast_type == "success":
            icon = "✅"
            bg_color = ModernTheme.COLORS['success']
        elif toast_type == "error":
            icon = "❌"
            bg_color = ModernTheme.COLORS['error']
        elif toast_type == "warning":
            icon = "⚠️"
            bg_color = ModernTheme.COLORS['warning']
        else:
            icon = "ℹ️"
            bg_color = ModernTheme.COLORS['info']

        self.icon_label.setText(icon)
        self.message_label.setText(message)

        self.setStyleSheet(f"""
            QWidget {{
                background-color: {bg_color};
                color: {ModernTheme.COLORS['text_white']};
                border-radius: {ModernTheme.SIZES['border_radius']}px;
            }}
        """)

        # 计算位置
        if self.parent_widget:
            parent_rect = self.parent_widget.geometry()
            self.adjustSize()
            x = parent_rect.right() - self.width() - 20
            y = parent_rect.top() + 20
            self.move(x, y)

        # 显示动画
        self.show()
        self.animation.setDuration(300)
        self.animation.setStartValue(0.0)
        self.animation.setEndValue(1.0)
        self.animation.start()

        # 自动隐藏
        if duration > 0:
            self.timer.start(duration)

    def hide_toast(self):
        """隐藏Toast"""
        self.timer.stop()
        self.animation.setDuration(300)
        self.animation.setStartValue(1.0)
        self.animation.setEndValue(0.0)
        self.animation.finished.connect(self.hide)
        self.animation.start()


class FileSorterApp3(QMainWindow):
    """文件自动分类工具 GUI 3.0 主应用"""

    def __init__(self):
        super().__init__()

        # 初始化日志
        self.logger = setup_module_logger('gui_3.0')

        # 初始化配置管理器
        self.config_manager = ConfigManager()

        # 加载主题设置
        self.load_theme_settings()

        # 初始化核心组件
        self.file_processor = FileProcessor(self.config_manager)
        self.document_generator = DocumentGenerator(self.config_manager)

        # 初始化UI组件
        self.toast = ToastNotification(self)

        # 状态变量
        self.current_directory = ""
        self.file_selection_state = {}
        self.category_files = {}
        self.processed_titles = set()
        self.is_processing = False

        # 设置窗口
        self.setup_window()
        self.setup_ui()
        self.setup_connections()

        # 恢复窗口状态
        self.restore_window_state()

        self.logger.info("GUI 3.0 应用启动")

    def load_theme_settings(self):
        """加载主题设置"""
        try:
            config = self.config_manager.get_config()
            font_size = config.get('font_size', '中等')
            color_scheme = config.get('color_scheme', '默认蓝色')

            ModernTheme.apply_font_size(font_size)
            ModernTheme.apply_theme(color_scheme)

            self.logger.info(f"已加载主题设置: 字体={font_size}, 颜色方案={color_scheme}")
        except Exception as e:
            self.logger.error(f"加载主题设置失败: {e}")
            ModernTheme.apply_font_size('中等')
            ModernTheme.apply_theme('默认蓝色')

    def setup_window(self):
        """设置主窗口"""
        self.setWindowTitle("文件自动分类工具 3.0 - 专业版")
        self.setMinimumSize(1400, 800)
        self.resize(1600, 1000)

        # 居中显示
        self.center_window()

        # 设置样式
        self.setStyleSheet(ModernTheme.get_stylesheet())

    def center_window(self):
        """窗口居中显示"""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)

    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建工具栏
        self.create_toolbar()

        # 创建主内容区域
        self.create_content_area()

        # 创建状态栏
        self.create_statusbar()

        main_layout.addWidget(self.toolbar_widget)
        main_layout.addWidget(self.content_splitter, 1)
        main_layout.addWidget(self.statusbar)

    def create_toolbar(self):
        """创建工具栏"""
        self.toolbar_widget = QFrame()
        self.toolbar_widget.setFixedHeight(ModernTheme.SIZES['toolbar_height'])
        self.toolbar_widget.setStyleSheet(f"""
            QFrame {{
                background-color: {ModernTheme.COLORS['nav_bg']};
                border-bottom: 1px solid {ModernTheme.COLORS['border']};
            }}
        """)

        layout = QHBoxLayout(self.toolbar_widget)
        layout.setContentsMargins(ModernTheme.SIZES['spacing_lg'],
                                 ModernTheme.SIZES['spacing_md'],
                                 ModernTheme.SIZES['spacing_lg'],
                                 ModernTheme.SIZES['spacing_md'])

        # 左侧按钮组
        left_group = QWidget()
        left_layout = QHBoxLayout(left_group)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(ModernTheme.SIZES['spacing_sm'])

        self.btn_select_dir = ModernButton("选择目录", "📁", "secondary")
        self.btn_start_process = ModernButton("开始分类", "🚀", "primary")

        left_layout.addWidget(self.btn_select_dir)
        left_layout.addWidget(self.btn_start_process)

        # 右侧控件组
        right_group = QWidget()
        right_layout = QHBoxLayout(right_group)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(ModernTheme.SIZES['spacing_sm'])

        # 搜索框
        search_label = QLabel("🔍")
        self.search_entry = QLineEdit()
        self.search_entry.setPlaceholderText("搜索文件...")
        self.search_entry.setFixedWidth(260)

        self.btn_settings = ModernButton("", "⚙️", "secondary")

        right_layout.addWidget(search_label)
        right_layout.addWidget(self.search_entry)
        right_layout.addWidget(self.btn_settings)

        # 添加到主布局
        layout.addWidget(left_group)
        layout.addStretch()
        layout.addWidget(right_group)

    def create_content_area(self):
        """创建主内容区域"""
        self.content_splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧目录树面板
        self.create_directory_panel()

        # 中央文件列表面板
        self.create_file_list_panel()

        # 右侧分类配置面板
        self.create_classification_panel()

        # 设置分割器比例
        self.content_splitter.setSizes([380, 800, 400])
        self.content_splitter.setChildrenCollapsible(False)

    def create_directory_panel(self):
        """创建目录面板"""
        self.directory_card = ModernCard("目录结构")
        content_layout = self.directory_card.get_content_layout()

        # 目录树工具栏
        toolbar = QWidget()
        toolbar_layout = QHBoxLayout(toolbar)
        toolbar_layout.setContentsMargins(0, 0, 0, 0)

        self.btn_expand_all = QPushButton("展开全部")
        self.btn_collapse_all = QPushButton("折叠全部")
        self.btn_refresh_tree = QPushButton("🔄")

        for btn in [self.btn_expand_all, self.btn_collapse_all, self.btn_refresh_tree]:
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {ModernTheme.COLORS['surface']};
                    border: 1px solid {ModernTheme.COLORS['border']};
                    border-radius: 4px;
                    padding: 4px 8px;
                }}
                QPushButton:hover {{
                    background-color: {ModernTheme.COLORS['hover']};
                }}
            """)

        toolbar_layout.addWidget(self.btn_expand_all)
        toolbar_layout.addWidget(self.btn_collapse_all)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.btn_refresh_tree)

        # 目录树
        self.directory_tree = QTreeWidget()
        self.directory_tree.setHeaderLabels(["名称", "类型", "大小"])
        self.directory_tree.setColumnWidth(0, 180)
        self.directory_tree.setColumnWidth(1, 60)
        self.directory_tree.setColumnWidth(2, 80)

        content_layout.addWidget(toolbar)
        content_layout.addWidget(self.directory_tree)

        self.content_splitter.addWidget(self.directory_card)

    def create_file_list_panel(self):
        """创建文件列表面板"""
        self.file_list_card = ModernCard("文件列表")
        content_layout = self.file_list_card.get_content_layout()

        # 文件类型过滤器
        filter_widget = QWidget()
        filter_layout = QHBoxLayout(filter_widget)
        filter_layout.setContentsMargins(0, 0, 0, 0)

        filter_label = QLabel("文件类型:")
        filter_layout.addWidget(filter_label)

        # 文件类型复选框
        self.file_type_checkboxes = {}
        file_types = [("TXT", "📄"), ("DOC", "📝"), ("PDF", "📋"), ("XLS", "📊"), ("PPT", "📈")]

        for file_type, icon in file_types:
            checkbox = QCheckBox(f"{icon} {file_type}")
            checkbox.setChecked(True)
            self.file_type_checkboxes[file_type.lower()] = checkbox
            filter_layout.addWidget(checkbox)

        filter_layout.addStretch()

        # 文件操作按钮
        self.btn_select_all = ModernButton("全选", "☑️", "secondary")
        self.btn_select_none = ModernButton("全不选", "☐", "secondary")
        self.btn_invert_selection = ModernButton("反选", "🔄", "secondary")
        self.btn_preview = ModernButton("预览", "👁️", "secondary")

        for btn in [self.btn_select_all, self.btn_select_none, self.btn_invert_selection, self.btn_preview]:
            filter_layout.addWidget(btn)

        # 文件列表表格
        self.file_table = QTableWidget()
        self.file_table.setColumnCount(6)
        self.file_table.setHorizontalHeaderLabels(["☑️", "文件名", "大小", "修改时间", "类型", "分类"])

        # 设置列宽
        header = self.file_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)

        self.file_table.setColumnWidth(0, 40)
        self.file_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

        content_layout.addWidget(filter_widget)
        content_layout.addWidget(self.file_table)

        self.content_splitter.addWidget(self.file_list_card)

    def create_classification_panel(self):
        """创建分类配置面板"""
        self.classification_card = ModernCard("分类配置")
        content_layout = self.classification_card.get_content_layout()

        # 分类规则列表
        rules_label = QLabel("当前分类规则")
        rules_label.setFont(ModernTheme.get_font('subtitle'))

        self.rules_tree = QTreeWidget()
        self.rules_tree.setHeaderLabels(["分类名称", "关键词", "匹配方式", "优先级"])
        self.rules_tree.setMaximumHeight(200)

        # 规则管理按钮
        rules_buttons = QWidget()
        rules_layout = QHBoxLayout(rules_buttons)
        rules_layout.setContentsMargins(0, 0, 0, 0)

        self.btn_manage_rules = ModernButton("规则管理", "⚙️", "secondary")
        self.btn_add_rule = ModernButton("添加规则", "➕", "primary")
        self.btn_generate_summary = ModernButton("文章汇总", "📄", "secondary")

        rules_layout.addWidget(self.btn_manage_rules)
        rules_layout.addWidget(self.btn_add_rule)
        rules_layout.addWidget(self.btn_generate_summary)

        content_layout.addWidget(rules_label)
        content_layout.addWidget(self.rules_tree)
        content_layout.addWidget(rules_buttons)
        content_layout.addStretch()

        self.content_splitter.addWidget(self.classification_card)

    def create_statusbar(self):
        """创建状态栏"""
        self.statusbar = QStatusBar()
        self.statusbar.setFixedHeight(ModernTheme.SIZES['statusbar_height'])

        # 状态标签
        self.status_label = QLabel("就绪")
        self.file_count_label = QLabel("文件: 0")
        self.selected_count_label = QLabel("已选: 0")

        self.statusbar.addWidget(self.status_label)
        self.statusbar.addPermanentWidget(self.selected_count_label)
        self.statusbar.addPermanentWidget(self.file_count_label)

    def setup_connections(self):
        """设置信号连接"""
        # 工具栏按钮
        self.btn_select_dir.clicked.connect(self.select_directory)
        self.btn_start_process.clicked.connect(self.start_processing)
        self.btn_settings.clicked.connect(self.open_settings)

        # 目录树按钮
        self.btn_expand_all.clicked.connect(self.directory_tree.expandAll)
        self.btn_collapse_all.clicked.connect(self.directory_tree.collapseAll)
        self.btn_refresh_tree.clicked.connect(self.refresh_directory_tree)

        # 文件列表按钮
        self.btn_select_all.clicked.connect(self.select_all_files)
        self.btn_select_none.clicked.connect(self.deselect_all_files)
        self.btn_invert_selection.clicked.connect(self.invert_file_selection)
        self.btn_preview.clicked.connect(self.preview_selected_file)

        # 分类规则按钮
        self.btn_manage_rules.clicked.connect(self.open_rule_manager)
        self.btn_add_rule.clicked.connect(self.add_new_rule)
        self.btn_generate_summary.clicked.connect(self.generate_summary)

        # 搜索框
        self.search_entry.textChanged.connect(self.on_search_change)

        # 目录树和文件表格
        self.directory_tree.itemSelectionChanged.connect(self.on_directory_select)
        self.file_table.cellClicked.connect(self.on_file_cell_clicked)
        self.file_table.itemDoubleClicked.connect(self.preview_selected_file)

        # 文件类型复选框
        for checkbox in self.file_type_checkboxes.values():
            checkbox.stateChanged.connect(self.refresh_file_list)

    def restore_window_state(self):
        """恢复窗口状态"""
        config = self.config_manager.get_config()

        # 恢复窗口几何形状
        geometry = config.get('window_geometry')
        if geometry:
            self.restoreGeometry(geometry)

        # 恢复窗口状态
        state = config.get('window_state')
        if state:
            self.restoreState(state)

        # 恢复分割器状态
        splitter_state = config.get('splitter_state')
        if splitter_state:
            self.content_splitter.restoreState(splitter_state)

    def save_window_state(self):
        """保存窗口状态"""
        config = {
            'window_geometry': self.saveGeometry(),
            'window_state': self.saveState(),
            'splitter_state': self.content_splitter.saveState(),
        }
        self.config_manager.update_config(config)

    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.is_processing:
            reply = QMessageBox.question(self, "确认", "正在处理文件，确定要退出吗？",
                                       QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            if reply == QMessageBox.StandardButton.No:
                event.ignore()
                return

        self.save_window_state()
        event.accept()

    # 核心功能方法
    def select_directory(self):
        """选择目录"""
        directory = QFileDialog.getExistingDirectory(self, "选择待分类的目录")
        if directory:
            self.current_directory = directory
            self.status_label.setText(f"已选择目录: {Path(directory).name}")
            self.refresh_directory_tree()
            self.refresh_file_list()
            self.logger.info(f"选择目录: {directory}")

    def start_processing(self):
        """开始文件分类处理"""
        if not self.current_directory:
            self.toast.show_toast("请先选择要分类的目录", "error")
            return

        if self.is_processing:
            return

        # 设置处理状态
        self.is_processing = True
        self.btn_start_process.set_loading(True)
        self.status_label.setText("正在分类文件...")

        # 在后台线程中处理
        self.processing_thread = ProcessingThread(self)
        self.processing_thread.progress_updated.connect(self.update_progress)
        self.processing_thread.processing_completed.connect(self.processing_completed)
        self.processing_thread.start()

    def update_progress(self, current: int, total: int):
        """更新进度"""
        self.status_label.setText(f"正在处理... ({current}/{total})")

    def processing_completed(self, count: int):
        """处理完成"""
        self.is_processing = False
        self.btn_start_process.set_loading(False)
        self.status_label.setText("就绪")
        self.toast.show_toast(f"分类完成（共 {count} 个文件）", "success")
        self.refresh_file_list()
        self.refresh_directory_tree()

    def refresh_directory_tree(self):
        """刷新目录树"""
        self.directory_tree.clear()

        if not self.current_directory or not os.path.exists(self.current_directory):
            return

        try:
            root_name = Path(self.current_directory).name or self.current_directory
            root_item = QTreeWidgetItem([f"📁 {root_name}", "文件夹", ""])
            root_item.setData(0, Qt.ItemDataRole.UserRole, self.current_directory)
            self.directory_tree.addTopLevelItem(root_item)

            self._add_directory_items(root_item, self.current_directory)
            root_item.setExpanded(True)

        except Exception as e:
            self.logger.error(f"刷新目录树失败: {str(e)}")
            self.toast.show_toast(f"刷新目录树失败: {str(e)}", "error")

    def _add_directory_items(self, parent_item: QTreeWidgetItem, directory_path: str):
        """添加目录项目"""
        try:
            items = []
            for item_name in os.listdir(directory_path):
                item_path = os.path.join(directory_path, item_name)
                try:
                    if os.path.isdir(item_path):
                        size = self._get_directory_size(item_path)
                        items.append(('dir', item_name, item_path, size))
                    elif os.path.isfile(item_path):
                        size = os.path.getsize(item_path)
                        items.append(('file', item_name, item_path, size))
                except (OSError, IOError):
                    continue

            # 排序：目录在前，文件在后
            items.sort(key=lambda x: (x[0] == 'file', x[1].lower()))

            for item_type, item_name, item_path, item_size in items:
                if item_type == 'dir':
                    child_item = QTreeWidgetItem([f"📁 {item_name}", "文件夹", self._format_file_size(item_size)])
                else:
                    icon = self._get_file_icon(Path(item_name).suffix.upper())
                    file_type = Path(item_name).suffix.upper() or "文件"
                    child_item = QTreeWidgetItem([f"{icon} {item_name}", file_type, self._format_file_size(item_size)])

                child_item.setData(0, Qt.ItemDataRole.UserRole, item_path)
                parent_item.addChild(child_item)

        except Exception as e:
            self.logger.error(f"添加目录项目失败: {str(e)}")

    def _get_directory_size(self, path: str) -> int:
        """获取目录大小"""
        total_size = 0
        try:
            for dirpath, _, filenames in os.walk(path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(filepath)
                    except (OSError, IOError):
                        continue
        except (OSError, IOError):
            pass
        return total_size

    def _format_file_size(self, size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"

    def _get_file_icon(self, ext: str) -> str:
        """获取文件图标"""
        icon_map = {
            '.TXT': '📄', '.DOC': '📝', '.DOCX': '📝', '.PDF': '📋',
            '.XLS': '📊', '.XLSX': '📊', '.PPT': '📈', '.PPTX': '📈',
        }
        return icon_map.get(ext, '📄')

    def refresh_file_list(self):
        """刷新文件列表"""
        self.file_table.setRowCount(0)
        self.file_selection_state.clear()

        if not self.current_directory or not os.path.exists(self.current_directory):
            self.file_count_label.setText("文件: 0")
            self.selected_count_label.setText("已选: 0")
            return

        try:
            files = []
            for item in os.listdir(self.current_directory):
                item_path = os.path.join(self.current_directory, item)
                if os.path.isfile(item_path):
                    files.append(item_path)

            # 过滤文件类型
            filtered_files = self._filter_files_by_type(files)

            # 添加到表格
            self.file_table.setRowCount(len(filtered_files))
            for row, file_path in enumerate(filtered_files):
                try:
                    file_name = os.path.basename(file_path)
                    file_size = self._format_file_size(os.path.getsize(file_path))
                    file_modified = time.strftime('%Y-%m-%d %H:%M', time.localtime(os.path.getmtime(file_path)))
                    file_ext = Path(file_name).suffix.upper()
                    file_category = self._get_file_category(file_name)

                    # 复选框
                    checkbox = QCheckBox()
                    checkbox.setChecked(True)
                    self.file_selection_state[file_path] = True
                    self.file_table.setCellWidget(row, 0, checkbox)

                    # 其他列
                    icon = self._get_file_icon(file_ext)
                    self.file_table.setItem(row, 1, QTableWidgetItem(f"{icon} {file_name}"))
                    self.file_table.setItem(row, 2, QTableWidgetItem(file_size))
                    self.file_table.setItem(row, 3, QTableWidgetItem(file_modified))
                    self.file_table.setItem(row, 4, QTableWidgetItem(file_ext))
                    self.file_table.setItem(row, 5, QTableWidgetItem(file_category))

                    # 存储文件路径
                    self.file_table.item(row, 1).setData(Qt.ItemDataRole.UserRole, file_path)

                    # 连接复选框信号
                    checkbox.stateChanged.connect(lambda state, path=file_path: self._update_file_selection(path, state))

                except Exception as e:
                    self.logger.error(f"添加文件 {file_path} 到列表失败: {str(e)}")
                    continue

            self.file_count_label.setText(f"文件: {len(filtered_files)}")
            self.selected_count_label.setText(f"已选: {len(filtered_files)}/{len(filtered_files)}")

        except Exception as e:
            self.logger.error(f"刷新文件列表失败: {str(e)}")
            self.toast.show_toast(f"刷新文件列表失败: {str(e)}", "error")

    def _filter_files_by_type(self, files: List[str]) -> List[str]:
        """根据选中的文件类型过滤文件"""
        # 检查哪些文件类型被选中
        selected_types = []
        for file_type, checkbox in self.file_type_checkboxes.items():
            if checkbox.isChecked():
                if file_type == 'txt':
                    selected_types.extend(['.txt'])
                elif file_type == 'doc':
                    selected_types.extend(['.doc', '.docx'])
                elif file_type == 'pdf':
                    selected_types.extend(['.pdf'])
                elif file_type == 'xls':
                    selected_types.extend(['.xls', '.xlsx'])
                elif file_type == 'ppt':
                    selected_types.extend(['.ppt', '.pptx'])

        if not selected_types:
            return files

        filtered = []
        for file_path in files:
            ext = Path(file_path).suffix.lower()
            if ext in selected_types:
                filtered.append(file_path)

        return filtered

    def _get_file_category(self, filename: str) -> str:
        """获取文件分类"""
        return self.config_manager.get_category_for_filename(filename) or "未分类"

    def _update_file_selection(self, file_path: str, state: int):
        """更新文件选择状态"""
        self.file_selection_state[file_path] = state == Qt.CheckState.Checked.value
        self.update_selection_count()

    def update_selection_count(self):
        """更新选择计数"""
        selected_count = sum(1 for selected in self.file_selection_state.values() if selected)
        total_count = len(self.file_selection_state)
        self.selected_count_label.setText(f"已选: {selected_count}/{total_count}")

    def on_directory_select(self):
        """目录选择事件"""
        current_item = self.directory_tree.currentItem()
        if current_item:
            path = current_item.data(0, Qt.ItemDataRole.UserRole)
            if path and path != self.current_directory:
                self.current_directory = path
                self.refresh_file_list()

    def on_file_cell_clicked(self, row: int, column: int):
        """文件表格单元格点击事件"""
        if column == 0:  # 复选框列
            checkbox = self.file_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(not checkbox.isChecked())

    def on_search_change(self, text: str):
        """搜索内容变化"""
        # 实现搜索过滤逻辑
        for row in range(self.file_table.rowCount()):
            item = self.file_table.item(row, 1)
            if item:
                visible = text.lower() in item.text().lower() if text else True
                self.file_table.setRowHidden(row, not visible)

    def select_all_files(self):
        """全选文件"""
        for row in range(self.file_table.rowCount()):
            checkbox = self.file_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(True)

    def deselect_all_files(self):
        """全不选文件"""
        for row in range(self.file_table.rowCount()):
            checkbox = self.file_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(False)

    def invert_file_selection(self):
        """反选文件"""
        for row in range(self.file_table.rowCount()):
            checkbox = self.file_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(not checkbox.isChecked())

    def preview_selected_file(self):
        """预览选中文件"""
        current_row = self.file_table.currentRow()
        if current_row >= 0:
            item = self.file_table.item(current_row, 1)
            if item:
                file_path = item.data(Qt.ItemDataRole.UserRole)
                if file_path and os.path.exists(file_path):
                    self._show_file_preview(file_path)
                else:
                    self.toast.show_toast("找不到选中的文件", "error")
        else:
            self.toast.show_toast("请先选择要预览的文件", "error")

    def _show_file_preview(self, file_path: str):
        """显示文件预览窗口"""
        preview_dialog = FilePreviewDialog(file_path, self)
        preview_dialog.exec()

    def open_settings(self):
        """打开设置窗口"""
        settings_dialog = SettingsDialog(self.config_manager, self)
        if settings_dialog.exec() == QDialog.DialogCode.Accepted:
            # 重新加载主题设置
            self.load_theme_settings()
            self.setStyleSheet(ModernTheme.get_stylesheet())
            self.toast.show_toast("设置已保存", "success")

    def open_rule_manager(self):
        """打开规则管理窗口"""
        rule_manager = RuleManagerDialog(self.config_manager, self)
        rule_manager.exec()
        self.refresh_rules_display()

    def add_new_rule(self):
        """添加新规则"""
        rule_editor = RuleEditorDialog(self.config_manager, self)
        if rule_editor.exec() == QDialog.DialogCode.Accepted:
            self.refresh_rules_display()
            self.toast.show_toast("规则添加成功", "success")

    def refresh_rules_display(self):
        """刷新分类规则显示"""
        self.rules_tree.clear()

        try:
            categories = self.config_manager.get_categories()
            for category in sorted(categories, key=lambda x: x.get('priority', 0), reverse=True):
                name = category.get('name', '未命名')
                keywords = category.get('keywords', [])
                match_type = category.get('match_type', 'contains')
                priority = category.get('priority', 1)

                keywords_text = ', '.join(keywords[:3])
                if len(keywords) > 3:
                    keywords_text += f" (+{len(keywords)-3})"

                match_type_text = {
                    'contains': '包含',
                    'startswith': '开头',
                    'default': '默认'
                }.get(match_type, '包含')

                icon = "🏠" if match_type == 'default' else "🔤" if match_type == 'startswith' else "🔍"

                item = QTreeWidgetItem([f"{icon} {name}", keywords_text, match_type_text, str(priority)])
                self.rules_tree.addTopLevelItem(item)

        except Exception as e:
            self.logger.error(f"刷新规则显示失败: {str(e)}")
            self.toast.show_toast(f"刷新规则失败: {str(e)}", "error")

    def generate_summary(self):
        """生成汇总文档"""
        if not self.category_files or not any(self.category_files.values()):
            self.toast.show_toast("没有可汇总的文件内容，请先进行分类处理", "error")
            return

        try:
            self.status_label.setText("正在生成汇总文档...")

            # 使用文档生成器
            config = self.config_manager.get_config()
            target_path = config.get('target_path', './已分类')

            if not os.path.exists(target_path):
                os.makedirs(target_path)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            summary_filename = f"文章汇总_{timestamp}.docx"
            summary_path = os.path.join(target_path, summary_filename)

            # 这里应该调用文档生成器的方法
            # self.document_generator.generate_summary(self.category_files, summary_path)

            self.status_label.setText("就绪")
            self.toast.show_toast(f"汇总文档已生成: {summary_filename}", "success")

            # 询问是否打开文档
            reply = QMessageBox.question(self, "打开文档", "是否立即打开汇总文档?",
                                       QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            if reply == QMessageBox.StandardButton.Yes:
                os.startfile(summary_path)

        except Exception as e:
            self.status_label.setText("就绪")
            self.logger.error(f"生成汇总文档失败: {e}")
            self.toast.show_toast(f"生成汇总文档失败: {str(e)}", "error")


class ProcessingThread(QThread):
    """文件处理线程"""

    progress_updated = pyqtSignal(int, int)
    processing_completed = pyqtSignal(int)

    def __init__(self, main_app):
        super().__init__()
        self.main_app = main_app

    def run(self):
        """运行文件处理"""
        try:
            # 获取选中的文件
            selected_files = []
            for file_path, is_selected in self.main_app.file_selection_state.items():
                if is_selected and os.path.isfile(file_path):
                    selected_files.append(file_path)

            total_files = len(selected_files)
            processed_count = 0

            # 初始化分类文件映射
            self.main_app.category_files = {}
            self.main_app.processed_titles = set()

            config = self.main_app.config_manager.get_config()
            target_path = config.get('target_path', './已分类')

            for file_path in selected_files:
                if not self.main_app.is_processing:
                    break

                try:
                    file_name = os.path.basename(file_path)
                    category = self.main_app._get_file_category(file_name)

                    # 创建目标目录
                    dest_dir = os.path.join(target_path, category)
                    if not os.path.exists(dest_dir):
                        os.makedirs(dest_dir)

                    # 复制文件
                    dest_path = os.path.join(dest_dir, file_name)
                    counter = 1
                    original_dest_path = dest_path
                    while os.path.exists(dest_path):
                        name, ext = os.path.splitext(original_dest_path)
                        dest_path = f"{name}_{counter}{ext}"
                        counter += 1

                    import shutil
                    shutil.copy2(file_path, dest_path)

                    processed_count += 1
                    self.progress_updated.emit(processed_count, total_files)

                except Exception as e:
                    self.main_app.logger.error(f"处理文件 {file_path} 失败: {e}")
                    continue

            self.processing_completed.emit(processed_count)

        except Exception as e:
            self.main_app.logger.error(f"文件处理失败: {e}")
            self.processing_completed.emit(0)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("文件自动分类工具 3.0")
    app.setApplicationVersion("3.0.0")
    app.setOrganizationName("FileSorter")

    # 设置应用图标（如果有的话）
    # app.setWindowIcon(QIcon("icon.ico"))

    # 创建主窗口
    window = FileSorterApp3()
    window.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
