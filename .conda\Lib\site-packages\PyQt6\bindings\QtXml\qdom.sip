// qdom.sip generated by MetaSIP
//
// This file is part of the QtXml Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDomImplementation
{
%TypeHeaderCode
#include <qdom.h>
%End

public:
    QDomImplementation();
    QDomImplementation(const QDomImplementation &);
    ~QDomImplementation();
    bool operator==(const QDomImplementation &) const;
    bool operator!=(const QDomImplementation &) const;
    bool hasFeature(const QString &feature, const QString &version) const;
    QDomDocumentType createDocumentType(const QString &qName, const QString &publicId, const QString &systemId);
    QDomDocument createDocument(const QString &nsURI, const QString &qName, const QDomDocumentType &doctype);

    enum InvalidDataPolicy
    {
        AcceptInvalidChars,
        DropInvalidChars,
        ReturnNullNode,
    };

    static QDomImplementation::InvalidDataPolicy invalidDataPolicy();
    static void setInvalidDataPolicy(QDomImplementation::InvalidDataPolicy policy);
    bool isNull();
};

class QDomNode
{
%TypeHeaderCode
#include <qdom.h>
%End

public:
    enum NodeType
    {
        ElementNode,
        AttributeNode,
        TextNode,
        CDATASectionNode,
        EntityReferenceNode,
        EntityNode,
        ProcessingInstructionNode,
        CommentNode,
        DocumentNode,
        DocumentTypeNode,
        DocumentFragmentNode,
        NotationNode,
        BaseNode,
        CharacterDataNode,
    };

    enum EncodingPolicy
    {
        EncodingFromDocument,
        EncodingFromTextStream,
    };

    QDomNode();
    QDomNode(const QDomNode &);
    ~QDomNode();
    bool operator==(const QDomNode &) const;
    bool operator!=(const QDomNode &) const;
    QDomNode insertBefore(const QDomNode &newChild, const QDomNode &refChild);
    QDomNode insertAfter(const QDomNode &newChild, const QDomNode &refChild);
    QDomNode replaceChild(const QDomNode &newChild, const QDomNode &oldChild);
    QDomNode removeChild(const QDomNode &oldChild);
    QDomNode appendChild(const QDomNode &newChild);
    bool hasChildNodes() const;
    QDomNode cloneNode(bool deep = true) const;
    void normalize();
    bool isSupported(const QString &feature, const QString &version) const;
    QString nodeName() const;
    QDomNode::NodeType nodeType() const;
    QDomNode parentNode() const;
    QDomNodeList childNodes() const;
    QDomNode firstChild() const;
    QDomNode lastChild() const;
    QDomNode previousSibling() const;
    QDomNode nextSibling() const;
    QDomNamedNodeMap attributes() const;
    QDomDocument ownerDocument() const;
    QString namespaceURI() const;
    QString localName() const;
    bool hasAttributes() const;
    QString nodeValue() const;
    void setNodeValue(const QString &);
    QString prefix() const;
    void setPrefix(const QString &pre);
    bool isAttr() const;
    bool isCDATASection() const;
    bool isDocumentFragment() const;
    bool isDocument() const;
    bool isDocumentType() const;
    bool isElement() const;
    bool isEntityReference() const;
    bool isText() const;
    bool isEntity() const;
    bool isNotation() const;
    bool isProcessingInstruction() const;
    bool isCharacterData() const;
    bool isComment() const;
    QDomNode namedItem(const QString &name) const;
    bool isNull() const;
    void clear();
    QDomAttr toAttr() const;
    QDomCDATASection toCDATASection() const;
    QDomDocumentFragment toDocumentFragment() const;
    QDomDocument toDocument() const;
    QDomDocumentType toDocumentType() const;
    QDomElement toElement() const;
    QDomEntityReference toEntityReference() const;
    QDomText toText() const;
    QDomEntity toEntity() const;
    QDomNotation toNotation() const;
    QDomProcessingInstruction toProcessingInstruction() const;
    QDomCharacterData toCharacterData() const;
    QDomComment toComment() const;
    void save(QTextStream &, int, QDomNode::EncodingPolicy = QDomNode::EncodingFromDocument) const /ReleaseGIL/;
    QDomElement firstChildElement(const QString &tagName = QString(), const QString &namespaceURI = QString()) const;
    QDomElement lastChildElement(const QString &tagName = QString(), const QString &namespaceURI = QString()) const;
    QDomElement previousSiblingElement(const QString &tagName = QString(), const QString &namespaceURI = QString()) const;
    QDomElement nextSiblingElement(const QString &taName = QString(), const QString &namespaceURI = QString()) const;
    int lineNumber() const;
    int columnNumber() const;
};

class QDomNodeList
{
%TypeHeaderCode
#include <qdom.h>
%End

public:
    QDomNodeList();
    QDomNodeList(const QDomNodeList &);
    ~QDomNodeList();
%If (- Qt_6_9_0)
    bool operator==(const QDomNodeList &) const;
%End
%If (- Qt_6_9_0)
    bool operator!=(const QDomNodeList &) const;
%End
    QDomNode item(int index) const;
    QDomNode at(int index) const;
    int length() const;
    int count() const /__len__/;
    int size() const;
    bool isEmpty() const;
};

class QDomDocumentType : public QDomNode
{
%TypeHeaderCode
#include <qdom.h>
%End

public:
    QDomDocumentType();
    QDomDocumentType(const QDomDocumentType &x);
    QString name() const;
    QDomNamedNodeMap entities() const;
    QDomNamedNodeMap notations() const;
    QString publicId() const;
    QString systemId() const;
    QString internalSubset() const;
    QDomNode::NodeType nodeType() const;
};

class QDomDocument : public QDomNode
{
%TypeHeaderCode
#include <qdom.h>
%End

public:
    QDomDocument();
    explicit QDomDocument(const QString &name);
    explicit QDomDocument(const QDomDocumentType &doctype);
    QDomDocument(const QDomDocument &x);
    ~QDomDocument();
    QDomElement createElement(const QString &tagName);
    QDomDocumentFragment createDocumentFragment();
    QDomText createTextNode(const QString &data);
    QDomComment createComment(const QString &data);
    QDomCDATASection createCDATASection(const QString &data);
    QDomProcessingInstruction createProcessingInstruction(const QString &target, const QString &data);
    QDomAttr createAttribute(const QString &name);
    QDomEntityReference createEntityReference(const QString &name);
    QDomNodeList elementsByTagName(const QString &tagname) const;
    QDomNode importNode(const QDomNode &importedNode, bool deep);
    QDomElement createElementNS(const QString &nsURI, const QString &qName);
    QDomAttr createAttributeNS(const QString &nsURI, const QString &qName);
    QDomNodeList elementsByTagNameNS(const QString &nsURI, const QString &localName);
    QDomElement elementById(const QString &elementId);
    QDomDocumentType doctype() const;
    QDomImplementation implementation() const;
    QDomElement documentElement() const;
    QDomNode::NodeType nodeType() const;
%If (Qt_6_5_0 -)

    enum class ParseOption
    {
        Default,
        UseNamespaceProcessing,
        PreserveSpacingOnlyNodes,
    };

%End
%If (Qt_6_5_0 -)
    typedef QFlags<QDomDocument::ParseOption> ParseOptions;
%End
%If (Qt_6_5_0 -)
    SIP_PYOBJECT setContent(QXmlStreamReader *reader, QDomDocument::ParseOptions options = QDomDocument::ParseOption::Default) /ReleaseGIL,TypeHint="Tuple [bool, str, int, int]"/;
%MethodCode
        QDomDocument::ParseResult pr;
        
        Py_BEGIN_ALLOW_THREADS
        pr = sipCpp->setContent(a0, *a1);
        Py_END_ALLOW_THREADS
        
        return sipBuildResult(NULL, "(bNnn)",
                (int)bool(pr),
                new QString(pr.errorMessage), sipType_QString, NULL,
                (long long)pr.errorLine,
                (long long)pr.errorColumn);
%End

%End
%If (Qt_6_5_0 -)
    SIP_PYOBJECT setContent(QIODevice *device, QDomDocument::ParseOptions options = QDomDocument::ParseOption::Default) /ReleaseGIL,TypeHint="Tuple [bool, str, int, int]"/;
%MethodCode
        QDomDocument::ParseResult pr;
        
        Py_BEGIN_ALLOW_THREADS
        pr = sipCpp->setContent(a0, *a1);
        Py_END_ALLOW_THREADS
        
        return sipBuildResult(NULL, "(bNnn)",
                (int)bool(pr),
                new QString(pr.errorMessage), sipType_QString, NULL,
                (long long)pr.errorLine,
                (long long)pr.errorColumn);
%End

%End
%If (Qt_6_5_0 -)
    SIP_PYOBJECT setContent(QAnyStringView data, QDomDocument::ParseOptions options = QDomDocument::ParseOption::Default) /TypeHint="Tuple [bool, str, int, int]"/;
%MethodCode
        QDomDocument::ParseResult pr;
        
        pr = sipCpp->setContent(*a0, *a1);
        
        return sipBuildResult(NULL, "(bNnn)",
                (int)bool(pr),
                new QString(pr.errorMessage), sipType_QString, NULL,
                (long long)pr.errorLine,
                (long long)pr.errorColumn);
%End

%End
    bool setContent(const QByteArray &text, bool namespaceProcessing, QString *errorMsg /Out/ = 0, int *errorLine = 0, int *errorColumn = 0);
    bool setContent(const QString &text, bool namespaceProcessing, QString *errorMsg /Out/ = 0, int *errorLine = 0, int *errorColumn = 0);
    bool setContent(QIODevice *dev, bool namespaceProcessing, QString *errorMsg /Out/ = 0, int *errorLine = 0, int *errorColumn = 0) /ReleaseGIL/;
%If (- Qt_6_5_0)
    bool setContent(const QByteArray &text, QString *errorMsg /Out/ = 0, int *errorLine = 0, int *errorColumn = 0);
%End
%If (- Qt_6_5_0)
    bool setContent(const QString &text, QString *errorMsg /Out/ = 0, int *errorLine = 0, int *errorColumn = 0);
%End
%If (- Qt_6_5_0)
    bool setContent(QIODevice *dev, QString *errorMsg /Out/ = 0, int *errorLine = 0, int *errorColumn = 0) /ReleaseGIL/;
%End
    bool setContent(QXmlStreamReader *reader, bool namespaceProcessing, QString *errorMsg /Out/ = 0, int *errorLine = 0, int *errorColumn = 0);
    QString toString(int indent = 1) const;
    QByteArray toByteArray(int indent = 1) const;
};

class QDomNamedNodeMap
{
%TypeHeaderCode
#include <qdom.h>
%End

public:
    QDomNamedNodeMap();
    QDomNamedNodeMap(const QDomNamedNodeMap &);
    ~QDomNamedNodeMap();
    bool operator==(const QDomNamedNodeMap &) const;
    bool operator!=(const QDomNamedNodeMap &) const;
    QDomNode namedItem(const QString &name) const;
    QDomNode setNamedItem(const QDomNode &newNode);
    QDomNode removeNamedItem(const QString &name);
    QDomNode item(int index) const;
    QDomNode namedItemNS(const QString &nsURI, const QString &localName) const;
    QDomNode setNamedItemNS(const QDomNode &newNode);
    QDomNode removeNamedItemNS(const QString &nsURI, const QString &localName);
    int length() const;
    int count() const /__len__/;
    int size() const;
    bool isEmpty() const;
    bool contains(const QString &name) const;
};

class QDomDocumentFragment : public QDomNode
{
%TypeHeaderCode
#include <qdom.h>
%End

public:
    QDomDocumentFragment();
    QDomDocumentFragment(const QDomDocumentFragment &x);
    QDomNode::NodeType nodeType() const;
};

class QDomCharacterData : public QDomNode
{
%TypeHeaderCode
#include <qdom.h>
%End

public:
    QDomCharacterData();
    QDomCharacterData(const QDomCharacterData &x);
    QString substringData(unsigned long offset, unsigned long count);
    void appendData(const QString &arg);
    void insertData(unsigned long offset, const QString &arg);
    void deleteData(unsigned long offset, unsigned long count);
    void replaceData(unsigned long offset, unsigned long count, const QString &arg);
    int length() const;
    QString data() const;
    void setData(const QString &);
    QDomNode::NodeType nodeType() const;
};

class QDomAttr : public QDomNode
{
%TypeHeaderCode
#include <qdom.h>
%End

public:
    QDomAttr();
    QDomAttr(const QDomAttr &x);
    QString name() const;
    bool specified() const;
    QDomElement ownerElement() const;
    QString value() const;
    void setValue(const QString &);
    QDomNode::NodeType nodeType() const;
};

class QDomElement : public QDomNode
{
%TypeHeaderCode
#include <qdom.h>
%End

public:
    QDomElement();
    QDomElement(const QDomElement &x);
    QString attribute(const QString &name, const QString &defaultValue = QString()) const;
    void setAttribute(const QString &name, const QString &value);
    void setAttribute(const QString &name, qlonglong value);
    void setAttribute(const QString &name, qulonglong value);
    void setAttribute(const QString &name, double value /Constrained/);
    void setAttribute(const QString &name, int value);
    void removeAttribute(const QString &name);
    QDomAttr attributeNode(const QString &name);
    QDomAttr setAttributeNode(const QDomAttr &newAttr);
    QDomAttr removeAttributeNode(const QDomAttr &oldAttr);
    QDomNodeList elementsByTagName(const QString &tagname) const;
    bool hasAttribute(const QString &name) const;
    QString attributeNS(const QString &nsURI, const QString &localName, const QString &defaultValue = QString()) const;
    void setAttributeNS(const QString &nsURI, const QString &qName, const QString &value);
    void setAttributeNS(const QString &nsURI, const QString &qName, double value /Constrained/);
    void setAttributeNS(const QString &nsURI, const QString &qName, SIP_PYOBJECT value /TypeHint="int"/);
%MethodCode
        qlonglong val = sipLong_AsLongLong(a2);
        
        if (!PyErr_Occurred())
        {
            sipCpp->setAttributeNS(*a0, *a1, val);
        }
        else
        {
            // If it is positive then it might fit an unsigned long long.
        
            qulonglong uval = sipLong_AsUnsignedLongLong(a2);
        
            if (!PyErr_Occurred())
            {
                sipCpp->setAttributeNS(*a0, *a1, uval);
            }
            else
            {
                sipError = (PyErr_ExceptionMatches(PyExc_OverflowError)
                        ? sipErrorFail : sipErrorContinue);
            }
        }
%End

    void removeAttributeNS(const QString &nsURI, const QString &localName);
    QDomAttr attributeNodeNS(const QString &nsURI, const QString &localName);
    QDomAttr setAttributeNodeNS(const QDomAttr &newAttr);
    QDomNodeList elementsByTagNameNS(const QString &nsURI, const QString &localName) const;
    bool hasAttributeNS(const QString &nsURI, const QString &localName) const;
    QString tagName() const;
    void setTagName(const QString &name);
    QDomNamedNodeMap attributes() const;
    QDomNode::NodeType nodeType() const;
    QString text() const;
};

class QDomText : public QDomCharacterData
{
%TypeHeaderCode
#include <qdom.h>
%End

public:
    QDomText();
    QDomText(const QDomText &x);
    QDomText splitText(int offset);
    QDomNode::NodeType nodeType() const;
};

class QDomComment : public QDomCharacterData
{
%TypeHeaderCode
#include <qdom.h>
%End

public:
    QDomComment();
    QDomComment(const QDomComment &x);
    QDomNode::NodeType nodeType() const;
};

class QDomCDATASection : public QDomText
{
%TypeHeaderCode
#include <qdom.h>
%End

public:
    QDomCDATASection();
    QDomCDATASection(const QDomCDATASection &x);
    QDomNode::NodeType nodeType() const;
};

class QDomNotation : public QDomNode
{
%TypeHeaderCode
#include <qdom.h>
%End

public:
    QDomNotation();
    QDomNotation(const QDomNotation &x);
    QString publicId() const;
    QString systemId() const;
    QDomNode::NodeType nodeType() const;
};

class QDomEntity : public QDomNode
{
%TypeHeaderCode
#include <qdom.h>
%End

public:
    QDomEntity();
    QDomEntity(const QDomEntity &x);
    QString publicId() const;
    QString systemId() const;
    QString notationName() const;
    QDomNode::NodeType nodeType() const;
};

class QDomEntityReference : public QDomNode
{
%TypeHeaderCode
#include <qdom.h>
%End

public:
    QDomEntityReference();
    QDomEntityReference(const QDomEntityReference &x);
    QDomNode::NodeType nodeType() const;
};

class QDomProcessingInstruction : public QDomNode
{
%TypeHeaderCode
#include <qdom.h>
%End

public:
    QDomProcessingInstruction();
    QDomProcessingInstruction(const QDomProcessingInstruction &x);
    QString target() const;
    QString data() const;
    void setData(const QString &d);
    QDomNode::NodeType nodeType() const;
};

QTextStream &operator<<(QTextStream &, const QDomNode &) /ReleaseGIL/;
%If (Qt_6_9_0 -)
bool operator!=(const QDomNodeList &lhs, const QDomNodeList &rhs);
%End
%If (Qt_6_9_0 -)
bool operator==(const QDomNodeList &lhs, const QDomNodeList &rhs);
%End
