// qambientsound.sip generated by MetaSIP
//
// This file is part of the QtSpatialAudio Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_5_0 -)

class QAmbientSound : public QObject
{
%TypeHeaderCode
#include <qambientsound.h>
%End

public:
    enum Loops
    {
        Infinite,
        Once,
    };

    explicit QAmbientSound(QAudioEngine *engine);
    virtual ~QAmbientSound();
    void setSource(const QUrl &url);
    QUrl source() const;
    int loops() const;
    void setLoops(int loops);
    bool autoPlay() const;
    void setAutoPlay(bool autoPlay);
    void setVolume(float volume);
    float volume() const;
    QAudioEngine *engine() const;

signals:
    void sourceChanged();
    void loopsChanged();
    void autoPlayChanged();
    void volumeChanged();

public slots:
    void play();
    void pause();
    void stop();
};

%End
