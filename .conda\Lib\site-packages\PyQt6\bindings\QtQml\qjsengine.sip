// qjsengine.sip generated by MetaSIP
//
// This file is part of the QtQml Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%ModuleCode
#include <qjsengine.h>
%End

class QJSEngine : public QObject
{
%TypeHeaderCode
#include <qjsengine.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QJSEngine, &sipType_QJSEngine, 9, 1},
        {sipName_QQmlComponent, &sipType_QQmlComponent, -1, 2},
        {sipName_QQmlContext, &sipType_QQmlContext, -1, 3},
        {sipName_QQmlEngineExtensionPlugin, &sipType_QQmlEngineExtensionPlugin, -1, 4},
        {sipName_QQmlExpression, &sipType_QQmlExpression, -1, 5},
        {sipName_QQmlExtensionPlugin, &sipType_QQmlExtensionPlugin, -1, 6},
        {sipName_QQmlFileSelector, &sipType_QQmlFileSelector, -1, 7},
        {sipName_QQmlImageProviderBase, &sipType_QQmlImageProviderBase, -1, 8},
        {sipName_QQmlPropertyMap, &sipType_QQmlPropertyMap, -1, -1},
        {sipName_QQmlEngine, &sipType_QQmlEngine, 10, -1},
        {sipName_QQmlApplicationEngine, &sipType_QQmlApplicationEngine, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    QJSEngine();
    explicit QJSEngine(QObject *parent /TransferThis/);
    virtual ~QJSEngine();
    QJSValue globalObject() const;
    QJSValue evaluate(const QString &program, const QString &fileName = QString(), int lineNumber = 1, SIP_PYLIST exceptionStackTrace /AllowNone,TypeHint="List[str]"/ = 0) /ReleaseGIL/;
%MethodCode
        QStringList *st;
        
        st = (a3 ? new QStringList() : SIP_NULLPTR);
        
        Py_BEGIN_ALLOW_THREADS
        sipRes = new QJSValue(sipCpp->evaluate(*a0, *a1, a2, st));
        Py_END_ALLOW_THREADS
        
        if (st)
        {
            for (qsizetype i = 0; i < st->size(); ++i)
            {
                QString *s = new QString(st->at(i));
                PyObject *s_obj = sipConvertFromNewType(s, sipType_QString, SIP_NULLPTR);
        
                if (s_obj)
                {
                    if (PyList_Append(a3, s_obj) < 0)
                    {
                        Py_DECREF(s_obj);
                        sipIsErr = 1;
                        break;
                    }
        
                    Py_DECREF(s_obj);
                }
                else
                {
                    delete s;
                    sipIsErr = 1;
                    break;
                }
            }
        
            if (sipIsErr)
            {
                delete sipRes;
                sipRes = SIP_NULLPTR;
            }
        
            delete st;
        }
%End

    QJSValue newObject();
    QJSValue newArray(uint length = 0);
    QJSValue newQObject(QObject *object /Transfer/);
    void collectGarbage();

    enum Extension /BaseType=Flag/
    {
        TranslationExtension,
        ConsoleExtension,
        GarbageCollectionExtension,
        AllExtensions,
    };

    typedef QFlags<QJSEngine::Extension> Extensions;
    void installExtensions(QJSEngine::Extensions extensions, const QJSValue &object = QJSValue());
    QJSValue newQMetaObject(const QMetaObject *metaObject);
    QJSValue importModule(const QString &fileName);
    QJSValue newErrorObject(QJSValue::ErrorType errorType, const QString &message = QString());
    void throwError(const QString &message);
%If (Qt_6_1_0 -)
    void throwError(const QJSValue &error);
%End
    void throwError(QJSValue::ErrorType errorType, const QString &message = QString());
    void setInterrupted(bool interrupted);
    bool isInterrupted() const;
    QString uiLanguage() const;
    void setUiLanguage(const QString &language);

    enum ObjectOwnership
    {
        CppOwnership,
        JavaScriptOwnership,
    };

    static void setObjectOwnership(QObject * /GetWrapper/, QJSEngine::ObjectOwnership);
%MethodCode
        QJSEngine::ObjectOwnership old = QJSEngine::objectOwnership(a0);
        
        QJSEngine::setObjectOwnership(a0, a1);
        
        if (old != a1 && !a0->parent())
        {
            if (old == QJSEngine::CppOwnership)
                sipTransferTo(a0Wrapper, Py_None);
            else
                sipTransferBack(a0Wrapper);
        }
%End

    static QJSEngine::ObjectOwnership objectOwnership(QObject *);
%If (Qt_6_1_0 -)
    bool hasError() const;
%End
%If (Qt_6_1_0 -)
    QJSValue catchError();
%End

signals:
    void uiLanguageChanged();

public:
%If (Qt_6_2_0 -)
    bool registerModule(const QString &moduleName, const QJSValue &value);
%End
%If (Qt_6_2_0 -)
    QJSValue newSymbol(const QString &name);
%End
%If (Qt_6_5_0 -)
    QJSValue toScriptValue(const QVariant &value);
%End
%If (Qt_6_5_0 -)
    QJSManagedValue toManagedValue(const QVariant &value);
%End
%If (Qt_6_5_0 -)
    QJSPrimitiveValue toPrimitiveValue(const QVariant &value);
%End
};

QJSEngine *qjsEngine(const QObject *);

%ModuleHeaderCode
#include "qpyqml_api.h"
%End

%PostInitialisationCode
qpyqml_post_init(sipModuleDict);
%End
