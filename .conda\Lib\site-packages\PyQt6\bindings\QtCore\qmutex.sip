// qmutex.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QMutex
{
%TypeHeaderCode
#include <qmutex.h>
%End

public:
    QMutex();
    ~QMutex();
// Methods from QBasicMutex.
void lock() /ReleaseGIL/;
bool tryLock() /ReleaseGIL/;
void unlock() /ReleaseGIL/;
%If (Qt_6_6_0 -)
    bool tryLock(QDeadlineTimer timeout) /ReleaseGIL/;
%End
    bool tryLock(int timeout) /ReleaseGIL/;

private:
    QMutex(const QMutex &);
};

class QRecursiveMutex
{
%TypeHeaderCode
#include <qmutex.h>
%End

public:
    QRecursiveMutex();
    ~QRecursiveMutex();
    void lock() /ReleaseGIL/;
%If (Qt_6_6_0 -)
    bool tryLock(QDeadlineTimer) /ReleaseGIL/ [bool (QDeadlineTimer timer = {})];
%End
    bool tryLock(int timeout = 0) /ReleaseGIL/;
    void unlock() /ReleaseGIL/;

private:
    QRecursiveMutex(const QRecursiveMutex &);
};
