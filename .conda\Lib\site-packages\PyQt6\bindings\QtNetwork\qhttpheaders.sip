// qhttpheaders.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_7_0 -)

class QHttpHeaders
{
%TypeHeaderCode
#include <qhttpheaders.h>
%End

public:
    enum class WellKnownHeader
    {
        AIM,
        Accept,
        AcceptAdditions,
        AcceptCH,
        AcceptDatetime,
        AcceptEncoding,
        AcceptFeatures,
        AcceptLanguage,
        AcceptPatch,
        AcceptPost,
        AcceptRanges,
        AcceptSignature,
        AccessControlAllowCredentials,
        AccessControlAllowHeaders,
        AccessControlAllowMethods,
        AccessControlAllowOrigin,
        AccessControlExposeHeaders,
        AccessControlMaxAge,
        AccessControlRequestHeaders,
        AccessControlRequestMethod,
        Age,
        Allow,
        ALPN,
        AltSvc,
        AltUsed,
        Alternates,
        ApplyToRedirectRef,
        AuthenticationControl,
        AuthenticationInfo,
        Authorization,
        CacheControl,
        CacheStatus,
        CalManagedID,
        CalDAVTimezones,
        CapsuleProtocol,
        CDNCacheControl,
        CDNLoop,
        CertNotAfter,
        CertNotBefore,
        ClearSiteData,
        ClientCert,
        ClientCertChain,
        Close,
        Connection,
        ContentDigest,
        ContentDisposition,
        ContentEncoding,
        ContentID,
        ContentLanguage,
        ContentLength,
        ContentLocation,
        ContentRange,
        ContentSecurityPolicy,
        ContentSecurityPolicyReportOnly,
        ContentType,
        Cookie,
        CrossOriginEmbedderPolicy,
        CrossOriginEmbedderPolicyReportOnly,
        CrossOriginOpenerPolicy,
        CrossOriginOpenerPolicyReportOnly,
        CrossOriginResourcePolicy,
        DASL,
        Date,
        DAV,
        DeltaBase,
        Depth,
        Destination,
        DifferentialID,
        DPoP,
        DPoPNonce,
        EarlyData,
        ETag,
        Expect,
        ExpectCT,
        Expires,
        Forwarded,
        From,
        Hobareg,
        Host,
        If,
        IfMatch,
        IfModifiedSince,
        IfNoneMatch,
        IfRange,
        IfScheduleTagMatch,
        IfUnmodifiedSince,
        IM,
        IncludeReferredTokenBindingID,
        KeepAlive,
        Label,
        LastEventID,
        LastModified,
        Link,
        Location,
        LockToken,
        MaxForwards,
        MementoDatetime,
        Meter,
        MIMEVersion,
        Negotiate,
        NEL,
        ODataEntityId,
        ODataIsolation,
        ODataMaxVersion,
        ODataVersion,
        OptionalWWWAuthenticate,
        OrderingType,
        Origin,
        OriginAgentCluster,
        OSCORE,
        OSLCCoreVersion,
        Overwrite,
        PingFrom,
        PingTo,
        Position,
        Prefer,
        PreferenceApplied,
        Priority,
        ProxyAuthenticate,
        ProxyAuthenticationInfo,
        ProxyAuthorization,
        ProxyStatus,
        PublicKeyPins,
        PublicKeyPinsReportOnly,
        Range,
        RedirectRef,
        Referer,
        Refresh,
        ReplayNonce,
        ReprDigest,
        RetryAfter,
        ScheduleReply,
        ScheduleTag,
        SecPurpose,
        SecTokenBinding,
        SecWebSocketAccept,
        SecWebSocketExtensions,
        SecWebSocketKey,
        SecWebSocketProtocol,
        SecWebSocketVersion,
        Server,
        ServerTiming,
        SetCookie,
        Signature,
        SignatureInput,
        SLUG,
        SoapAction,
        StatusURI,
        StrictTransportSecurity,
        Sunset,
        SurrogateCapability,
        SurrogateControl,
        TCN,
        TE,
        Timeout,
        Topic,
        Traceparent,
        Tracestate,
        Trailer,
        TransferEncoding,
        TTL,
        Upgrade,
        Urgency,
        UserAgent,
        VariantVary,
        Vary,
        Via,
        WantContentDigest,
        WantReprDigest,
        WWWAuthenticate,
        XContentTypeOptions,
        XFrameOptions,
        AcceptCharset,
        CPEPInfo,
        Pragma,
        ProtocolInfo,
        ProtocolQuery,
    };

    QHttpHeaders();
    QHttpHeaders(const QHttpHeaders &other);
    ~QHttpHeaders();
    void swap(QHttpHeaders &other /Constrained/);
    bool append(QAnyStringView name, QAnyStringView value);
    bool append(QHttpHeaders::WellKnownHeader name, QAnyStringView value);
    bool insert(qsizetype i, QAnyStringView name, QAnyStringView value);
    bool insert(qsizetype i, QHttpHeaders::WellKnownHeader name, QAnyStringView value);
    bool replace(qsizetype i, QAnyStringView name, QAnyStringView newValue);
    bool replace(qsizetype i, QHttpHeaders::WellKnownHeader name, QAnyStringView newValue);
    bool contains(QAnyStringView name) const;
    bool contains(QHttpHeaders::WellKnownHeader name) const;
    void clear();
    void removeAll(QAnyStringView name);
    void removeAll(QHttpHeaders::WellKnownHeader name);
    void removeAt(qsizetype i);
    QByteArrayView value(QAnyStringView name, QByteArrayView defaultValue = {}) const;
    QByteArrayView value(QHttpHeaders::WellKnownHeader name, QByteArrayView defaultValue = {}) const;
    QList<QByteArray> values(QAnyStringView name) const;
    QList<QByteArray> values(QHttpHeaders::WellKnownHeader name) const;
    QByteArrayView valueAt(qsizetype i) const;
    QString nameAt(qsizetype i) const [QLatin1StringView (qsizetype i)];
    QByteArray combinedValue(QAnyStringView name) const;
    QByteArray combinedValue(QHttpHeaders::WellKnownHeader name) const;
    qsizetype size() const;
    void reserve(qsizetype size);
    bool isEmpty() const;
    static QByteArrayView wellKnownHeaderName(QHttpHeaders::WellKnownHeader name);
    static QHttpHeaders fromListOfPairs(const QList<std::pair<QByteArray, QByteArray>> &headers);
    QList<std::pair<QByteArray, QByteArray>> toListOfPairs() const;
%If (Qt_6_8_0 -)
    bool replaceOrAppend(QHttpHeaders::WellKnownHeader name, QAnyStringView newValue);
%End
%If (Qt_6_8_0 -)
    bool replaceOrAppend(QAnyStringView name, QAnyStringView newValue);
%End
};

%End
