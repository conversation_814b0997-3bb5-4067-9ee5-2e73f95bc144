// qsvgwidget.sip generated by MetaSIP
//
// This file is part of the QtSvgWidgets Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSvgWidget : public QWidget
{
%TypeHeaderCode
#include <qsvgwidget.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QGraphicsSvgItem, &sipType_QGraphicsSvgItem, -1, 1},
        {sipName_QSvgWidget, &sipType_QSvgWidget, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    QSvgWidget(QWidget *parent /TransferThis/ = 0);
    QSvgWidget(const QString &file, QWidget *parent /TransferThis/ = 0);
    virtual ~QSvgWidget();
    QSvgRenderer *renderer() const;
    virtual QSize sizeHint() const;

public slots:
    void load(const QString &file) /ReleaseGIL/;
    void load(const QByteArray &contents);

protected:
    virtual void paintEvent(QPaintEvent *event);

public:
%If (Qt_6_7_0 -)
    QtSvg::Options options() const;
%End
%If (Qt_6_7_0 -)
    void setOptions(QtSvg::Options options);
%End
};
