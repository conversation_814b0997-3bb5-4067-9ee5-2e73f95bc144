// qtimer.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QTimer : public QObject
{
%TypeHeaderCode
#include <qtimer.h>
%End

public:
    explicit QTimer(QObject *parent /TransferThis/ = 0);
    virtual ~QTimer();
    bool isActive() const;
    int timerId() const;
    void setInterval(int msec);
    int interval() const;
    bool isSingleShot() const;
    void setSingleShot(bool asingleShot);
    static void singleShot(int msec, SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/);
%MethodCode
        QObject *receiver;
        QByteArray slot_signature;
        
        if ((sipError = pyqt6_get_connection_parts(a1, 0, "()", true, &receiver, slot_signature)) == sipErrorNone)
        {
            QTimer::singleShot(a0, receiver, slot_signature.constData());
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(1, a1);
        }
%End

    static void singleShot(int msec, Qt::TimerType timerType, SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/);
%MethodCode
        QObject *receiver;
        QByteArray slot_signature;
        
        if ((sipError = pyqt6_get_connection_parts(a2, 0, "()", true, &receiver, slot_signature)) == sipErrorNone)
        {
            QTimer::singleShot(a0, a1, receiver, slot_signature.constData());
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(2, a2);
        }
%End

public slots:
    void start(int msec);
    void start();
    void stop();

signals:
    void timeout();

protected:
    virtual void timerEvent(QTimerEvent *);

public:
    void setTimerType(Qt::TimerType atype);
    Qt::TimerType timerType() const;
    int remainingTime() const;
%If (Qt_6_8_0 -)
    int id() const;
%MethodCode
        sipRes = qToUnderlying<Qt::TimerId>(sipCpp->id());
%End

%End
};
