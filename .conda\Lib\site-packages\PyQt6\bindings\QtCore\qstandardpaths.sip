// qstandardpaths.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QStandardPaths
{
%TypeHeaderCode
#include <qstandardpaths.h>
%End

public:
    enum StandardLocation
    {
        DesktopLocation,
        DocumentsLocation,
        FontsLocation,
        ApplicationsLocation,
        MusicLocation,
        MoviesLocation,
        PicturesLocation,
        TempLocation,
        HomeLocation,
        CacheLocation,
        GenericDataLocation,
        RuntimeLocation,
        ConfigLocation,
        DownloadLocation,
        GenericCacheLocation,
        GenericConfigLocation,
        AppDataLocation,
        AppLocalDataLocation,
        AppConfigLocation,
%If (Qt_6_4_0 -)
        PublicShareLocation,
%End
%If (Qt_6_4_0 -)
        TemplatesLocation,
%End
%If (Qt_6_7_0 -)
        StateLocation,
%End
%If (Qt_6_7_0 -)
        GenericStateLocation,
%End
    };

    static QString writableLocation(QStandardPaths::StandardLocation type);
    static QStringList standardLocations(QStandardPaths::StandardLocation type);

    enum LocateOption /BaseType=Flag/
    {
        LocateFile,
        LocateDirectory,
    };

    typedef QFlags<QStandardPaths::LocateOption> LocateOptions;
    static QString locate(QStandardPaths::StandardLocation type, const QString &fileName, QStandardPaths::LocateOptions options = QStandardPaths::LocateFile);
    static QStringList locateAll(QStandardPaths::StandardLocation type, const QString &fileName, QStandardPaths::LocateOptions options = QStandardPaths::LocateFile);
    static QString displayName(QStandardPaths::StandardLocation type);
    static QString findExecutable(const QString &executableName, const QStringList &paths = QStringList());
    static void setTestModeEnabled(bool testMode);

private:
    QStandardPaths();
    ~QStandardPaths();
};
